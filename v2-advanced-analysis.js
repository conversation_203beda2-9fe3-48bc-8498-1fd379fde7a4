/**
 * v2 接口高级签名算法分析
 * 基于 v1 算法正确的前提下，深入分析 v2 的差异
 */

const crypto = require('crypto');

// v2 真实请求数据
const v2Request = {
  timestamp: 1755239241,
  nonce: 'QL4MKOwQFtSmnhCOmNjde',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '460D94E7C6980A6973494BC75D075905',
  url: '/ai-search/chatApi/v2/chat',
  method: 'POST',
  body: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363022964585267589","question":"你好!","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363022965811450053","chatId":"363022965811450053","files":[],"reference":[],"role":"user","status":"local","content":"你好!","userAction":"","agentId":""}'
};

console.log('=== v2 接口高级签名算法分析 ===\n');

/**
 * 测试函数
 */
function testMD5(description, input, expected) {
  const hash = crypto.createHash('md5').update(input).digest('hex').toUpperCase();
  const match = hash === expected;
  console.log(`${match ? '✅' : '❌'} ${description}`);
  console.log(`   输入: ${input.substring(0, 100)}${input.length > 100 ? '...' : ''}`);
  console.log(`   输出: ${hash}`);
  console.log(`   期望: ${expected}`);
  console.log(`   长度: ${input.length}\n`);
  return match;
}

/**
 * 分析 v2 可能的特殊规则
 */
function analyzeV2SpecialRules() {
  console.log('1. 测试 v2 是否不使用 body（可能只用于 SSE 流式响应）:');
  
  // 假设 v2 接口因为是流式响应，可能不包含 body 在签名中
  let input = `${v2Request.timestamp}${v2Request.nonce}`;
  testMD5('仅 timestamp + nonce', input, v2Request.sign);
  
  // 测试包含 URL 但不包含 body
  input = `${v2Request.timestamp}${v2Request.url}${v2Request.nonce}`;
  testMD5('timestamp + url + nonce', input, v2Request.sign);
  
  // 测试包含 method 但不包含 body
  input = `${v2Request.timestamp}${v2Request.method}${v2Request.nonce}`;
  testMD5('timestamp + method + nonce', input, v2Request.sign);
  
  // 测试 method + url 组合
  input = `${v2Request.timestamp}${v2Request.method} ${v2Request.url}${v2Request.nonce}`;
  testMD5('timestamp + "method url" + nonce', input, v2Request.sign);
  
  console.log('2. 测试 v2 特有的路径处理:');
  
  // 测试只使用 API 路径的最后部分
  const pathParts = v2Request.url.split('/');
  const lastPart = pathParts[pathParts.length - 1]; // "chat"
  input = `${v2Request.timestamp}${lastPart}${v2Request.nonce}`;
  testMD5('timestamp + 路径最后部分("chat") + nonce', input, v2Request.sign);
  
  // 测试使用 API 类型
  const apiType = pathParts[pathParts.length - 2]; // "v2"
  input = `${v2Request.timestamp}${apiType}${v2Request.nonce}`;
  testMD5('timestamp + API版本("v2") + nonce', input, v2Request.sign);
  
  // 测试组合
  input = `${v2Request.timestamp}${apiType}${lastPart}${v2Request.nonce}`;
  testMD5('timestamp + "v2chat" + nonce', input, v2Request.sign);
  
  console.log('3. 测试设备相关的规则:');
  
  // 测试包含设备ID
  input = `${v2Request.timestamp}${v2Request.deviceId}${v2Request.nonce}`;
  testMD5('timestamp + deviceId + nonce', input, v2Request.sign);
  
  // 测试设备ID的不同部分
  const deviceParts = v2Request.deviceId.split('_');
  const deviceHash = deviceParts[0];
  const deviceSuffix = deviceParts[1];
  
  input = `${v2Request.timestamp}${deviceHash}${v2Request.nonce}`;
  testMD5('timestamp + 设备哈希部分 + nonce', input, v2Request.sign);
  
  input = `${v2Request.timestamp}${deviceSuffix}${v2Request.nonce}`;
  testMD5('timestamp + 设备后缀部分 + nonce', input, v2Request.sign);
}

/**
 * 测试字符串变换
 */
function testStringTransformations() {
  console.log('4. 测试字符串变换和编码:');
  
  const baseString = `${v2Request.timestamp}${v2Request.body}${v2Request.nonce}`;
  
  // 测试 Base64 编码
  const base64Encoded = Buffer.from(baseString).toString('base64');
  testMD5('Base64编码后的MD5', base64Encoded, v2Request.sign);
  
  // 测试 URL 编码
  const urlEncoded = encodeURIComponent(baseString);
  testMD5('URL编码后的MD5', urlEncoded, v2Request.sign);
  
  // 测试反转字符串
  const reversed = baseString.split('').reverse().join('');
  testMD5('反转字符串的MD5', reversed, v2Request.sign);
  
  // 测试添加固定前缀/后缀
  const prefixed = `dangbei${baseString}`;
  testMD5('添加"dangbei"前缀', prefixed, v2Request.sign);
  
  const suffixed = `${baseString}dangbei`;
  testMD5('添加"dangbei"后缀', suffixed, v2Request.sign);
}

/**
 * 测试不同的参数组合顺序
 */
function testParameterOrders() {
  console.log('5. 测试不同的参数组合顺序:');
  
  // 不同的参数顺序
  const combinations = [
    `${v2Request.nonce}${v2Request.timestamp}${v2Request.body}`,
    `${v2Request.body}${v2Request.timestamp}${v2Request.nonce}`,
    `${v2Request.timestamp}${v2Request.nonce}${v2Request.body}`,
    `${v2Request.nonce}${v2Request.body}${v2Request.timestamp}`,
    `${v2Request.body}${v2Request.nonce}${v2Request.timestamp}`
  ];
  
  const descriptions = [
    'nonce + timestamp + body',
    'body + timestamp + nonce',
    'timestamp + nonce + body',
    'nonce + body + timestamp',
    'body + nonce + timestamp'
  ];
  
  combinations.forEach((combo, index) => {
    testMD5(descriptions[index], combo, v2Request.sign);
  });
}

/**
 * 测试可能的密钥或盐值
 */
function testWithSalts() {
  console.log('6. 测试可能的密钥或盐值:');
  
  const salts = [
    'chat', 'v2', 'stream', 'sse', 'chatApi',
    'dangbei-chat', 'ai-chat', 'chat-v2',
    '2024', '2025', 'secret-v2'
  ];
  
  const baseString = `${v2Request.timestamp}${v2Request.body}${v2Request.nonce}`;
  
  salts.forEach(salt => {
    // 前缀
    testMD5(`盐值前缀: "${salt}" + 基础串`, `${salt}${baseString}`, v2Request.sign);
    
    // 后缀
    testMD5(`盐值后缀: 基础串 + "${salt}"`, `${baseString}${salt}`, v2Request.sign);
    
    // 中间插入
    testMD5(`盐值中间: timestamp + "${salt}" + body + nonce`, 
      `${v2Request.timestamp}${salt}${v2Request.body}${v2Request.nonce}`, v2Request.sign);
  });
}

/**
 * 测试特殊的 JSON 处理
 */
function testJSONProcessing() {
  console.log('7. 测试特殊的 JSON 处理方式:');
  
  const bodyObj = JSON.parse(v2Request.body);
  
  // 测试只使用特定字段
  const keyFields = ['conversationId', 'question', 'model'];
  keyFields.forEach(field => {
    if (bodyObj[field]) {
      const input = `${v2Request.timestamp}${bodyObj[field]}${v2Request.nonce}`;
      testMD5(`仅使用 ${field} 字段`, input, v2Request.sign);
    }
  });
  
  // 测试字段组合
  const questionAndModel = `${bodyObj.question}${bodyObj.model}`;
  const input = `${v2Request.timestamp}${questionAndModel}${v2Request.nonce}`;
  testMD5('question + model 字段组合', input, v2Request.sign);
  
  // 测试 conversationId
  const convInput = `${v2Request.timestamp}${bodyObj.conversationId}${v2Request.nonce}`;
  testMD5('仅 conversationId', convInput, v2Request.sign);
}

// 执行所有分析
analyzeV2SpecialRules();
testStringTransformations();
testParameterOrders();
testWithSalts();
testJSONProcessing();

console.log('=== 分析完成 ===');
console.log('\n总结:');
console.log('- v1 接口使用标准的 timestamp + body + nonce 规则');
console.log('- v2 接口使用了不同的签名算法');
console.log('- 需要在浏览器中查找 v2 特有的签名生成代码');
console.log('- 可能存在版本判断逻辑或专门的 chatApi 处理函数');
