# 当贝AI签名 整合分析报告

## 1. 背景与目标
- 目标：基于实际线上 Next.js 产物与抓包请求，定位 headers.sign 的生成逻辑，复刻最小可用的签名算法，保证后续可持续分析与对齐。
- 场景：请求路径前缀均为 /ai-search，调用包括：generateId、conversationApi/v1/batch/create、chatApi/v2/chat 等。

## 2. 关键发现（来自 _app chunk）
- 在 pages/_app-72ae859153e99355.js 中，存在 HTTP 客户端实例 b.S（即 58309 模块导出的 fetch 客户端），对其：
  - setConfig({ baseUrl: "https://ai-api.dangbei.net/ai-search" })
  - 注册了 request 拦截器统一注入 headers：timestamp、nonce、sign、token、appType、lang、client-ver、appVersion、deviceId 等
- 生成签名的关键调用序列：
  - 先得到 Map M：
    - timestamp = moment("second").unix()
    - nonce = (0,k.Ak)() // 随机
    - sign = S()( `${timestamp}${O(e,t)}${nonce}` ).toUpperCase()
    - version = "v1"
  - 将上述放入 Map 后写入请求头
- 线索指向 O(e,t) 是“规范化请求串”。

## 3. 我们的实现策略（最小一致实现）
- 由于 O(e,t) 的细节在打包后较难直接阅读，我们采用最小一致实现，先保证“形式对齐”，后续再细化：
  - 规范化串 normalized ≈ `${METHOD} ${PATH(+query)}`
    - METHOD 取请求方法的大写
    - PATH 从 url 提取，仅保留路径与 query；若误传入了完整 URL（含协议与域名），我们自动截断
  - 最终签名：sign = MD5(`${timestamp}${normalized}${nonce}`).toUpperCase()
- 已在代码中落地：
  - SignatureParams 增加 method/url 字段
  - HttpClient 请求拦截器传入 method/url
  - SignatureUtils 依据上述规则生成 sign

## 4. 代码变更要点
- types/common.ts
  - 新增 SignatureParams.method 与 SignatureParams.url
- utils/signature.ts
  - 由“键值对排序+MD5”改为“timestamp + (METHOD SP PATH) + nonce -> MD5 upper”
  - 详细中文注释，解释设计缘由、URL 规范化与降级策略
- services/http-client.ts
  - 在生成签名时，注入 method 与 url 给 SignatureUtils
  - 仍保持中文调试日志，便于后续排查
- 文档：调用流程.md
  - 顶部加入签名生成规则说明，便于与线上对照

## 5. 证据摘录
- setConfig 与拦截器：
```
b.S.setConfig({baseUrl:"https://ai-api.dangbei.net/ai-search"});
b.S.interceptors.request.use(function(){ ...
  e.headers.set("timestamp", `${c}`);
  e.headers.set("nonce", l);
  e.headers.set("sign", u);
  ...
});
```
- sign 计算序列：
```
o = moment("second").unix();
i = (0,k.Ak)();
a = O(e,t); // 规范化请求串
s = S()(`${o}${a}${i}`).toUpperCase();
```

## 6. 当前局限与后续方向
- 仍未知 O(e,t) 是否还拼入 body 或其它字段；当前选择最小一致（METHOD + 空格 + PATH[+query]）的原因：
  - 线上 chunk 中没有明显对 body 或 headers 序列化后参与签名的证据
  - 真实样本中 token 常为空；但 _app 代码里支持 token 注入，如 token 参与签名则需进一步对齐
- 下一步建议：
  1) 在浏览器 DevTools 中对 _app chunk pretty-print 后，针对 O(e,t) 具体实现打断点，观察其入参与返回值
  2) 收集多条真实请求样本（不同路径/不同 body），对照新算法的输出，逐步细化 O(e,t) 的构造
  3) 若发现还需拼入 body 或特定字段，迭代 SignatureUtils 以匹配

## 7. 调试与验收
- 调试方式：
  - 运行我们的请求（generateId / batch/create / chat），在调试模式下观察控制台中文日志：
    - 打印请求方法、URL、参与签名的字段（隐藏 sign 部分位）
  - 浏览器 Network 面板比对 headers 与线上一致性
- 验收标准：
  - headers 中 timestamp/nonce/sign 与线上规则一致（形式与时序）
  - 若后续加入更精细的 O(e,t)，以真实样本验证可重现

## 8. 结论
- 已经将项目侧的签名逻辑与线上 _app 拦截器的“结构与流程”对齐（尤其是签名串结构），并保留中文注释与日志，便于进一步细化与核对。
- 文档与代码均已更新，便于团队成员快速了解背景、证据、实现与限制。

