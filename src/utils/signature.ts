/**
 * 签名生成工具
 * 用于生成当贝AI API请求所需的安全签名
 */

import { createHash } from 'crypto';
import { SignatureParams, DeviceConfig } from '../types';
import { SignatureV2Utils } from './signature-v2';

/**
 * 签名生成器类
 * 负责生成API请求的MD5签名
 */
export class SignatureUtils {
  /**
   * 生成API请求签名
   *
   * 注意：经过详尽的逆向工程分析，我们无法完全匹配当贝AI的官方签名算法。
   * 当贝AI可能使用了专有的加密算法、服务器端密钥或其他动态参数。
   * 当前实现提供了一个标准的签名生成方式，但API调用可能会失败。
   * 程序设计了完善的错误处理和本地备用方案来应对这种情况。
   *
   * @param params 签名参数
   * @returns MD5签名字符串（大写）
   */
  public static generateSignature(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    // 检测接口版本并应用相应的签名算法
    const isV2Interface = SignatureV2Utils.isV2Interface(params.url || '');

    if (isV2Interface) {
      // v2接口使用特殊的签名算法
      if (process.env['NODE_ENV'] === 'development') {
        console.log('🔍 检测到v2接口，使用v2签名算法:', params.url);
      }
      return SignatureV2Utils.generateV2Signature(params, extra);
    }

    // v1接口使用标准的签名算法（已验证正确）
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔍 检测到v1接口，使用标准签名算法:', params.url);
    }

    // 新逻辑（与线上 _app chunk 拦截器一致）：
    // sign = MD5( `${timestamp}${O(e,t)}${nonce}` ).toUpperCase()
    // 其中 O(e,t) 为“规范化请求串”：通常由 method + url(+可选query/body规则) 组成。
    // 为避免与线上细节不一致导致失败，我们采取最小可用实现：method + url。

    // 1) 基础字段（必须）
    const { timestamp, nonce } = params;

    // 2) 规范化请求串 O(e,t)：严格还原 GET/POST 规则
    const method = (params.method || 'POST').toUpperCase();
    let normalized = '';

    if (method === 'GET') {
      let url = params.url || '';
      // 如果传入了完整URL，仅取 pathname + search，然后再提取查询串
      if (url.startsWith('http://') || url.startsWith('https://')) {
        try {
          const u = new URL(url);
          url = u.pathname + (u.search || '');
        } catch {
          // 解析失败时，保留原值
        }
      }
      const qIndex = url.indexOf('?');
      normalized = qIndex !== -1 ? url.substring(qIndex + 1) : '';
    } else if (method === 'POST') {
      // 优先使用 bodyRaw（由拦截器保证与实际发送一致）
      if (typeof params.bodyRaw === 'string') {
        normalized = params.bodyRaw;
      } else if (typeof (params.data as any) === 'string') {
        // 兼容：若上层已提供字符串化的 data
        normalized = params.data as string;
      } else if (params.data && typeof params.data === 'object') {
        // 兜底：将对象序列化为 JSON 字符串（注意：可能与实际发送不一致，建议上层传 bodyRaw）
        try {
          normalized = JSON.stringify(params.data);
        } catch {
          normalized = '';
        }
      } else {
        normalized = '';
      }
    } else {
      normalized = '';
    }

    // 3) 组合签名串：timestamp(秒) + normalized + nonce
    const signString = `${timestamp}${normalized}${nonce}`;

    // 4) 生成MD5哈希签名（大写格式）
    const signature = createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();

    // 在调试模式下输出详细的签名信息
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 签名生成详情:');
      console.log('  - 算法类型: 标准MD5哈希');
      console.log('  - 签名字符串(摘要):', `${signString.length}B md5=${createHash('md5').update(signString).digest('hex').substring(0,8)}`);
      console.log('  - 生成的签名:', signature);
      if (extra) {
        console.log('  - 参与签名的设备/应用信息:', JSON.stringify(extra));
      }
      if (params.method === 'GET') {
        console.log('  - 规范化串(GET-qs)(摘要):', `${normalized.length}B md5=${createHash('md5').update(normalized).digest('hex').substring(0,8)}`);
      } else if (params.method === 'POST') {
        const norm = normalized || '';
        console.log('  - 规范化串(POST-body)(摘要):', `${norm.length}B md5=${createHash('md5').update(norm).digest('hex').substring(0,8)}`);
      } else {
        console.log('  - 规范化串(其他方法): 空串');
      }
      console.log('  - 提示: 当前规则来自 _app chunk 反推，若线上变更需同步调整');
    }

    return signature;
  }

  /**
   * 生成随机nonce字符串
   *
   * 基于对真实API请求的分析，nonce格式为：
   * - 长度：21个字符
   * - 字符集：数字、大小写字母、连字符(-)
   * - 示例：'111g0amuaFUAic500cMI-'
   *
   * @param length nonce长度，默认为21（与观察到的真实请求一致）
   * @returns 随机nonce字符串
   */
  public static generateNonce(length: number = 21): string {
    // 基于真实API请求分析的字符集：数字、字母、连字符
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-';
    let result = '';

    // 生成随机字符串
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  /**
   * 获取当前时间戳（秒）
   * 
   * @returns Unix时间戳
   */
  public static getTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   * 
   * @returns Unix时间戳（毫秒）
   */
  public static getTimestampMs(): number {
    return Date.now();
  }

  /**
   * 验证签名是否有效
   * 
   * @param signature 待验证的签名
   * @param params 签名参数
   * @returns 是否有效
   */
  public static verifySignature(signature: string, params: SignatureParams): boolean {
    const expectedSignature = this.generateSignature(params);
    return signature.toUpperCase() === expectedSignature;
  }
}
