/**
 * 最终签名算法测试
 * 尝试一些极端的可能性和自定义算法
 */

const crypto = require('crypto');

// 真实请求参数
const realRequest = {
  timestamp: 1755252943,
  nonce: '111g0amuaFUAic500cMI-',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '9CC214FB53DDAF31DC1BFE453D14C468',
  requestBody: {
    conversationList: [{
      metaData: {
        chatModelConfig: {},
        superAgentPath: "/chat"
      },
      shareId: "",
      isAnonymous: false,
      source: ""
    }]
  }
};

console.log('=== 最终签名算法测试 ===\n');

// 测试函数
function testHash(description, input, expected) {
  console.log(`\n--- ${description} ---`);
  console.log(`输入: ${input.substring(0, 100)}${input.length > 100 ? '...' : ''}`);
  
  const md5 = crypto.createHash('md5').update(input).digest('hex').toUpperCase();
  console.log(`MD5: ${md5}`);
  
  if (md5 === expected) {
    console.log('🎉 匹配!');
    return true;
  }
  
  console.log('❌ 无匹配');
  return false;
}

// 1. 测试可能遗漏的HTTP头参数
console.log('1. 测试包含HTTP头参数:');

const httpHeaders = [
  'appType=6',
  'appVersion=1.1.18',
  'client-ver=1.0.2',
  'lang=zh',
  'content-type=application/json'
];

// 尝试包含所有HTTP头
const allHeaders = httpHeaders.concat([
  `deviceId=${realRequest.deviceId}`,
  `nonce=${realRequest.nonce}`,
  `timestamp=${realRequest.timestamp}`
]).sort().join('&');

testHash('所有HTTP头参数', allHeaders, realRequest.sign);

// 2. 测试可能的字符串变换
console.log('\n2. 测试字符串变换:');

const baseString = `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`;

// 尝试不同的字符编码
const encodingTests = [
  Buffer.from(baseString, 'utf8').toString('hex'),
  Buffer.from(baseString, 'utf8').toString('base64'),
  encodeURIComponent(baseString),
  baseString.replace(/[=&]/g, ''),
  baseString.toUpperCase(),
  baseString.toLowerCase()
];

for (let i = 0; i < encodingTests.length; i++) {
  if (testHash(`编码测试 ${i + 1}`, encodingTests[i], realRequest.sign)) {
    break;
  }
}

// 3. 测试可能的数学运算
console.log('\n3. 测试数学运算:');

// 尝试对时间戳进行数学运算
const mathTests = [
  `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp * 1000}`,
  `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp + 1}`,
  `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp - 1}`,
  `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${Math.floor(realRequest.timestamp / 1000)}`
];

for (let i = 0; i < mathTests.length; i++) {
  if (testHash(`数学运算 ${i + 1}`, mathTests[i], realRequest.sign)) {
    break;
  }
}

// 4. 测试可能的固定前缀/后缀
console.log('\n4. 测试固定前缀/后缀:');

const prefixSuffixTests = [
  `api${baseString}`,
  `${baseString}api`,
  `dangbei${baseString}dangbei`,
  `sign${baseString}`,
  `${baseString}sign`,
  `v1${baseString}`,
  `${baseString}v1`
];

for (let i = 0; i < prefixSuffixTests.length; i++) {
  if (testHash(`前缀后缀 ${i + 1}`, prefixSuffixTests[i], realRequest.sign)) {
    break;
  }
}

// 5. 测试可能的自定义算法模拟
console.log('\n5. 测试自定义算法模拟:');

// 尝试模拟一些可能的自定义变换
function customTransform1(str) {
  // 简单的字符替换
  return str.replace(/[aeiou]/g, '0').replace(/[AEIOU]/g, '1');
}

function customTransform2(str) {
  // 字符位置交换
  const chars = str.split('');
  for (let i = 0; i < chars.length - 1; i += 2) {
    [chars[i], chars[i + 1]] = [chars[i + 1], chars[i]];
  }
  return chars.join('');
}

function customTransform3(str) {
  // 简单的Caesar密码
  return str.split('').map(char => {
    if (char >= 'a' && char <= 'z') {
      return String.fromCharCode(((char.charCodeAt(0) - 97 + 3) % 26) + 97);
    }
    if (char >= 'A' && char <= 'Z') {
      return String.fromCharCode(((char.charCodeAt(0) - 65 + 3) % 26) + 65);
    }
    return char;
  }).join('');
}

const customTests = [
  customTransform1(baseString),
  customTransform2(baseString),
  customTransform3(baseString)
];

for (let i = 0; i < customTests.length; i++) {
  if (testHash(`自定义变换 ${i + 1}`, customTests[i], realRequest.sign)) {
    break;
  }
}

// 6. 最后的尝试：分析签名本身
console.log('\n6. 分析目标签名:');
console.log(`目标签名: ${realRequest.sign}`);
console.log(`签名长度: ${realRequest.sign.length}`);
console.log(`十六进制分析:`);

// 将签名分解为字节
const signBytes = [];
for (let i = 0; i < realRequest.sign.length; i += 2) {
  signBytes.push(realRequest.sign.substr(i, 2));
}

console.log(`字节数组: [${signBytes.join(', ')}]`);
console.log(`十进制: [${signBytes.map(b => parseInt(b, 16)).join(', ')}]`);

// 检查是否有模式
const uniqueBytes = [...new Set(signBytes)];
console.log(`唯一字节数: ${uniqueBytes.length}/16`);
console.log(`重复字节: ${signBytes.length - uniqueBytes.length > 0 ? '是' : '否'}`);

console.log('\n=== 结论 ===');
console.log('经过详尽的测试，我们无法逆向出当贝AI的签名算法。');
console.log('可能的原因:');
console.log('1. 使用了专有的加密算法');
console.log('2. 包含了服务器端的密钥或动态参数');
console.log('3. 签名可能包含了时间窗口或其他动态因素');
console.log('4. 可能需要特定的客户端环境或状态');
console.log('\n建议:');
console.log('1. 继续使用当前的错误处理和本地备用方案');
console.log('2. 监控API错误码的变化，寻找新的线索');
console.log('3. 考虑联系当贝AI官方获取正确的API文档');
console.log('4. 保持代码的灵活性，以便将来快速适配正确的算法');
