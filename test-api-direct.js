/**
 * 直接测试API调用，尝试不同的请求格式
 */

const axios = require('axios');

// 测试不同的API调用方式
async function testApiDirectly() {
  const baseURL = 'https://ai-api.dangbei.net';
  
  // 测试1: 最简单的请求，不带任何签名
  console.log('\n=== 测试1: 无签名请求 ===');
  try {
    const response = await axios.post(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      timestamp: Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试2: 带基础头信息但无签名
  console.log('\n=== 测试2: 基础头信息无签名 ===');
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const response = await axios.post(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      timestamp: Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'timestamp': timestamp.toString(),
        'nonce': 'test-nonce-123',
        'deviceId': 'test-device-id',
        'token': ''
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试3: 尝试GET请求
  console.log('\n=== 测试3: GET请求 ===');
  try {
    const response = await axios.get(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试4: 尝试不同的端点
  console.log('\n=== 测试4: 健康检查端点 ===');
  try {
    const response = await axios.get(`${baseURL}/health`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试5: 尝试根路径
  console.log('\n=== 测试5: 根路径 ===');
  try {
    const response = await axios.get(baseURL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });
    console.log('成功:', response.status, response.headers['content-type']);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试6: 尝试使用空签名
  console.log('\n=== 测试6: 空签名 ===');
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const response = await axios.post(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      timestamp: Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'timestamp': timestamp.toString(),
        'nonce': 'test-nonce-123',
        'deviceId': 'test-device-id',
        'sign': '',
        'token': ''
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }

  // 测试7: 尝试使用固定签名
  console.log('\n=== 测试7: 固定签名 ===');
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const response = await axios.post(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      timestamp: Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'timestamp': timestamp.toString(),
        'nonce': 'test-nonce-123',
        'deviceId': 'test-device-id',
        'sign': '00000000000000000000000000000000',
        'token': ''
      },
      timeout: 10000
    });
    console.log('成功:', response.data);
  } catch (error) {
    console.log('失败:', error.response?.status, error.response?.data || error.message);
  }
}

// 运行测试
console.log('开始直接API测试...');
testApiDirectly().then(() => {
  console.log('\n=== 测试完成 ===');
}).catch(error => {
  console.error('测试出错:', error.message);
});
