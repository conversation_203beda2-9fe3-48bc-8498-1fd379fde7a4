# 当贝AI WebAssembly 签名模块使用指南

## 概述

本文档详细介绍如何在 Node.js 环境中使用当贝AI的 WebAssembly 签名模块。该模块基于对原始 JavaScript 代码的逆向分析，提供了与官方签名算法兼容的实现。

## 功能特性

### 🔧 多种实现方式
- **WebAssembly 模块**: 直接调用编译后的 WASM 文件
- **JavaScript 备用**: 纯 JavaScript 实现的备用算法
- **统一接口**: 自动选择最佳实现方式

### 🛡️ 可靠性保障
- 自动降级机制
- 错误重试机制
- 详细的错误处理
- 性能监控

### 🚀 易于使用
- 简单的 API 接口
- 详细的中文文档
- 完整的使用示例
- TypeScript 类型支持

## 快速开始

### 1. 准备 WASM 文件

首先需要将 WAT 文件编译为 WASM 文件：

```bash
# 使用编译脚本
node scripts/compile-wasm.js sign_bg.wat sign_bg.wasm

# 或者手动使用 wabt 工具
wat2wasm sign_bg.wat -o sign_bg.wasm
```

### 2. 基础使用

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function example() {
  // 创建签名器实例
  const signer = new UnifiedSignature({
    wasmPath: './sign_bg.wasm',
    debug: true
  });

  // 初始化
  await signer.initialize();

  // 生成签名
  const signature = await signer.generateSignature(
    '{"question":"你好","model":"kimi-k2-0711-preview"}',
    '1755239241:random_nonce_123'
  );

  console.log('生成的签名:', signature);
}
```

### 3. 快速签名

```javascript
const { quickSign } = require('./src/wasm/unified-signature');

// 一行代码生成签名
const signature = await quickSign(
  'test_data',
  '1755239241:nonce_123'
);
```

## 详细使用方法

### WebAssembly 模块直接使用

```javascript
const DangbeiSignatureWasm = require('./src/wasm/dangbei-signature-wasm');

async function useWasm() {
  const signer = new DangbeiSignatureWasm();
  
  try {
    // 初始化 WASM 模块
    await signer.initialize('./sign_bg.wasm');
    
    // 生成签名
    const signature = signer.generateSignature(
      'request_data',
      'timestamp_nonce'
    );
    
    console.log('WASM 签名:', signature);
    
  } catch (error) {
    console.error('WASM 签名失败:', error.message);
  } finally {
    signer.destroy();
  }
}
```

### 备用实现使用

```javascript
const { FallbackSignature } = require('./src/wasm/fallback-signature');

function useFallback() {
  const signer = new FallbackSignature({
    debug: true,
    secretKey: 'custom_secret_key'
  });
  
  // 生成签名
  const signature = signer.generateSignature(
    'request_data',
    'timestamp_nonce'
  );
  
  console.log('备用签名:', signature);
  
  // 验证签名
  const isValid = signer.verifySignature(
    'request_data',
    'timestamp_nonce',
    signature
  );
  
  console.log('签名验证:', isValid);
}
```

### 统一接口高级用法

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function advancedUsage() {
  const signer = new UnifiedSignature({
    wasmPath: './sign_bg.wasm',
    preferWasm: true,
    enableFallback: true,
    retryAttempts: 3,
    debug: true
  });

  await signer.initialize();

  // 获取状态信息
  const status = signer.getStatus();
  console.log('签名器状态:', status);

  // 批量生成签名
  const requests = [
    { param1: 'data1', param2: 'nonce1' },
    { param1: 'data2', param2: 'nonce2' },
    { param1: 'data3', param2: 'nonce3' }
  ];
  
  const signatures = await signer.generateSignatureBatch(requests);
  console.log('批量签名结果:', signatures);

  // 性能测试
  const perfResults = await signer.performanceTest(100);
  console.log('性能测试结果:', perfResults);

  // 手动切换模式
  signer.switchMode('fallback');
  
  // 清理资源
  signer.destroy();
}
```

## API 参考

### UnifiedSignature 类

#### 构造函数选项

```javascript
const options = {
  wasmPath: './sign_bg.wasm',     // WASM 文件路径
  preferWasm: true,               // 优先使用 WASM
  enableFallback: true,           // 启用备用方案
  retryAttempts: 3,               // 重试次数
  debug: false,                   // 调试模式
  secretKey: 'custom_key'         // 自定义密钥
};
```

#### 主要方法

- `initialize()`: 初始化签名器
- `generateSignature(param1, param2)`: 生成签名
- `generateSignatureBatch(requests)`: 批量生成签名
- `getStatus()`: 获取状态信息
- `switchMode(mode)`: 切换签名模式
- `performanceTest(iterations)`: 性能测试
- `destroy()`: 清理资源

### DangbeiSignatureWasm 类

#### 主要方法

- `initialize(wasmPath)`: 初始化 WASM 模块
- `generateSignature(str1, str2)`: 生成签名
- `writeStringToMemory(str)`: 写入字符串到内存
- `getStringFromMemory(ptr, len)`: 从内存读取字符串
- `destroy()`: 清理资源

### FallbackSignature 类

#### 主要方法

- `generateSignature(param1, param2)`: 生成签名
- `verifySignature(param1, param2, signature)`: 验证签名
- `getAlgorithmInfo()`: 获取算法信息

## 实际应用示例

### 模拟当贝AI API调用

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function callDangbeiAPI() {
  const signer = new UnifiedSignature();
  await signer.initialize();

  // 准备API请求数据
  const requestData = {
    stream: true,
    botCode: "AI_SEARCH",
    conversationId: "363022964585267589",
    question: "你好!",
    model: "doubao-1_6-thinking",
    chatOption: {
      searchKnowledge: false,
      searchAllKnowledge: false,
      searchSharedKnowledge: false
    }
  };

  // 生成时间戳和随机数
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = generateRandomNonce();
  const deviceId = 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm';

  // 生成签名
  const requestBody = JSON.stringify(requestData);
  const signature = await signer.generateSignature(
    requestBody,
    `${timestamp}:${nonce}`
  );

  // 构建完整的请求头
  const headers = {
    'Accept': '*/*',
    'Content-Type': 'application/json',
    'appType': '6',
    'appVersion': '1.1.17-22',
    'client-ver': '1.0.2',
    'deviceId': deviceId,
    'lang': 'zh',
    'nonce': nonce,
    'sign': signature,
    'timestamp': timestamp.toString(),
    'token': ''
  };

  console.log('API 请求头:', headers);
  console.log('请求体:', requestBody);

  // 这里可以使用 fetch 或 axios 发送实际请求
  // const response = await fetch('https://ai-api.dangbei.net/ai-search/chatApi/v2/chat', {
  //   method: 'POST',
  //   headers,
  //   body: requestBody
  // });

  signer.destroy();
}

function generateRandomNonce() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  let result = '';
  for (let i = 0; i < 17; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
```

## 故障排除

### 常见问题

1. **WASM 文件加载失败**
   - 检查文件路径是否正确
   - 确保 WASM 文件完整且未损坏
   - 验证 Node.js 版本支持 WebAssembly

2. **签名生成失败**
   - 检查输入参数格式
   - 启用调试模式查看详细日志
   - 尝试使用备用实现

3. **性能问题**
   - 使用批量签名接口
   - 考虑缓存签名结果
   - 监控内存使用情况

### 调试技巧

```javascript
// 启用详细调试日志
const signer = new UnifiedSignature({
  debug: true
});

// 检查状态
console.log('签名器状态:', signer.getStatus());

// 性能分析
const perfResults = await signer.performanceTest(10);
console.log('性能结果:', perfResults);
```

## 注意事项

1. **安全性**: 不要在客户端暴露签名逻辑
2. **性能**: 大量签名操作建议使用批量接口
3. **兼容性**: 确保 Node.js 版本支持 WebAssembly
4. **错误处理**: 始终包含适当的错误处理逻辑

## 更新日志

- **v1.0.0**: 初始版本，支持 WebAssembly 和备用实现
- 基于对当贝AI原始代码的逆向分析
- 提供完整的中文文档和示例
