/**
 * 测试v2接口检测和签名生成
 */

const { <PERSON><PERSON><PERSON>Provider } = require('./dist');

async function testV2Detection() {
  console.log('=== v2接口检测测试 ===\n');

  // 创建Provider实例
  const provider = new DangbeiProvider({
    debug: true
  });

  try {
    // 测试v1接口（对话创建）
    console.log('1. 测试v1接口检测:');
    const conversation = await provider.createConversation();
    console.log('✅ v1接口（对话创建）成功\n');
    
    // 测试v2接口（聊天）
    console.log('2. 测试v2接口检测:');
    try {
      const response = await provider.chatSync({
        conversationId: conversation.conversationId,
        question: '你好'
      });
      console.log('✅ v2接口（聊天）成功');
      console.log('响应:', response.substring(0, 100) + '...');
    } catch (error) {
      console.log('❌ v2接口（聊天）失败:', error.message);
      console.log('这是预期的，因为v2签名算法仍需要进一步调试');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    provider.destroy();
    console.log('\n🧹 资源已清理');
  }
}

// 运行测试
if (require.main === module) {
  testV2Detection()
    .then(() => {
      console.log('\n🎉 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { testV2Detection };
