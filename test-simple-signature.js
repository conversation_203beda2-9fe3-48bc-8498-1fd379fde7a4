/**
 * 测试简化的签名算法
 * 尝试找到正确的签名生成方式
 */

const axios = require('axios');
const crypto = require('crypto');

// 生成简单的签名
function generateSimpleSignature(timestamp, nonce, deviceId) {
  // 尝试最简单的组合
  const combinations = [
    // 方法1: 直接连接
    `${timestamp}${nonce}${deviceId}`,
    // 方法2: 用分隔符连接
    `${timestamp}&${nonce}&${deviceId}`,
    // 方法3: 键值对格式
    `timestamp=${timestamp}&nonce=${nonce}&deviceId=${deviceId}`,
    // 方法4: 不同顺序
    `deviceId=${deviceId}&nonce=${nonce}&timestamp=${timestamp}`,
    // 方法5: 只用时间戳和设备ID
    `${timestamp}${deviceId}`,
    // 方法6: 只用nonce和设备ID
    `${nonce}${deviceId}`,
    // 方法7: 添加固定盐值
    `${timestamp}${nonce}${deviceId}dangbei`,
    // 方法8: 使用固定密钥
    `dangbei${timestamp}${nonce}${deviceId}`,
  ];

  return combinations.map(str => ({
    method: str,
    md5: crypto.createHash('md5').update(str).digest('hex').toUpperCase(),
    sha1: crypto.createHash('sha1').update(str).digest('hex').toUpperCase(),
    sha256: crypto.createHash('sha256').update(str).digest('hex').toUpperCase()
  }));
}

// 测试API调用
async function testWithSignature(signature, method) {
  const baseURL = 'https://ai-api.dangbei.net';
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = 'test-nonce-123';
  const deviceId = 'test-device-id-123';

  try {
    const response = await axios.post(`${baseURL}/ai-search/commonApi/v1/generateId`, {
      timestamp: Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'timestamp': timestamp.toString(),
        'nonce': nonce,
        'deviceId': deviceId,
        'sign': signature,
        'token': ''
      },
      timeout: 10000
    });
    
    console.log(`✅ 成功! 方法: ${method}`);
    console.log('响应:', response.data);
    return true;
  } catch (error) {
    const status = error.response?.status;
    const errCode = error.response?.data?.errCode;
    const errMessage = error.response?.data?.errMessage;
    
    console.log(`❌ 失败: ${method}`);
    console.log(`   状态码: ${status}, 错误码: ${errCode}, 消息: ${errMessage}`);
    
    // 如果错误码变化，说明我们在正确的方向上
    if (errCode && errCode !== '4001' && errCode !== '5002') {
      console.log(`🔍 新的错误码: ${errCode} - 可能有进展!`);
    }
    
    return false;
  }
}

// 主测试函数
async function runSignatureTests() {
  console.log('开始测试简化签名算法...\n');
  
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = 'test-nonce-123';
  const deviceId = 'test-device-id-123';
  
  console.log('测试参数:');
  console.log(`  timestamp: ${timestamp}`);
  console.log(`  nonce: ${nonce}`);
  console.log(`  deviceId: ${deviceId}\n`);
  
  const signatures = generateSimpleSignature(timestamp, nonce, deviceId);
  
  for (let i = 0; i < signatures.length; i++) {
    const sig = signatures[i];
    
    console.log(`\n=== 测试 ${i + 1}: ${sig.method} ===`);
    
    // 测试MD5
    console.log(`MD5: ${sig.md5}`);
    const md5Success = await testWithSignature(sig.md5, `${sig.method} (MD5)`);
    
    if (md5Success) {
      console.log('🎉 找到正确的签名方法!');
      break;
    }
    
    // 如果MD5失败，尝试SHA1
    console.log(`SHA1: ${sig.sha1}`);
    const sha1Success = await testWithSignature(sig.sha1, `${sig.method} (SHA1)`);
    
    if (sha1Success) {
      console.log('🎉 找到正确的签名方法!');
      break;
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n=== 测试完成 ===');
}

// 运行测试
runSignatureTests().catch(error => {
  console.error('测试出错:', error.message);
});
