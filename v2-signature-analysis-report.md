# v2 接口签名算法分析报告

## 执行摘要

通过对当贝AI API的深入分析，我们确认了 `chatApi/v2/chat` 接口使用与 v1 接口完全不同的签名算法。

## 关键发现

### 1. v1 vs v2 签名算法差异

| 接口版本 | 签名算法 | 验证状态 |
|----------|----------|----------|
| **v1** | `MD5(timestamp + body + nonce).toUpperCase()` | ✅ **已验证正确** |
| **v2** | **未知算法** | ❌ **需要进一步分析** |

### 2. 分析数据对比

#### v1 接口（已验证）
```bash
URL: /ai-search/conversationApi/v1/batch/create
时间戳: 1755252943
Nonce: 111g0amuaFUAic500cMI-
签名: 9CC214FB53DDAF31DC1BFE453D14C468
算法: MD5(1755252943 + {"conversationList":[...]} + 111g0amuaFUAic500cMI-)
```

#### v2 接口（未解决）
```bash
URL: /ai-search/chatApi/v2/chat
时间戳: 1755239241
Nonce: QL4MKOwQFtSmnhCOmNjde
签名: 460D94E7C6980A6973494BC75D075905
算法: ❓ 未知
```

### 3. 排除的可能性

我们已经测试并排除了以下算法：

#### 标准哈希算法
- ❌ MD5(timestamp + body + nonce)
- ❌ SHA1(timestamp + body + nonce)  
- ❌ SHA256(timestamp + body + nonce)

#### 参数组合变体
- ❌ 不同的参数顺序（5种组合）
- ❌ 包含/排除 URL 路径
- ❌ 包含/排除 HTTP 方法
- ❌ 包含/排除设备ID

#### 字符串处理变体
- ❌ Base64 编码
- ❌ URL 编码
- ❌ 字符串反转
- ❌ 添加常见前缀/后缀

#### HMAC 算法
- ❌ 测试了10种可能的密钥
- ❌ HMAC-MD5 和 HMAC-SHA1

#### JSON 处理变体
- ❌ 仅使用特定字段
- ❌ 字段排序
- ❌ 紧凑格式

## 技术分析

### 断点代码分析

您提供的断点代码片段：
```javascript
case 3:
    return o = C()().startOf("second").unix(),
    i = (0, k.Ak)(),
    a = O(e, t),
    s = S()("".concat(o).concat(a).concat(i)).toUpperCase(),
    (c = new Map).set("timestamp", o),
    c.set("nonce", i),
    c.set("sign", s),
    c.set("version", "v1"),  // 🔑 关键：明确标注为 v1
    [2, c];
```

**关键发现**：
- 这个断点专门处理 `version: "v1"` 的请求
- `chatApi/v2/chat` 不会进入这个代码分支
- 说明存在专门的 v2 处理逻辑

### 可能的 v2 特殊性

1. **流式响应特性**
   - v2 接口支持 SSE 流式响应
   - 可能使用不同的签名策略

2. **请求体复杂性**
   - v2 请求体更大更复杂（415 vs 128 字符）
   - 包含 `stream: true` 等特殊字段

3. **版本隔离**
   - 可能有专门的 v2 签名函数
   - 可能使用服务器端密钥

## 下一步行动计划

### 1. 浏览器调试（优先级：高）

使用我们提供的 `v2-browser-debug-guide.md` 进行：
- 查找版本判断逻辑
- 设置网络拦截断点
- 监控签名生成过程

### 2. 代码搜索策略

在浏览器开发者工具中搜索：
```javascript
// 高优先级搜索词
"v2"
"chatApi"
"version.*v2"
"if.*v2"
"chat.*sign"

// 可能的函数名
"generateV2Sign"
"chatApiSign"
"streamSign"
```

### 3. 网络监控

对比多个 v2 请求：
- 不同的 conversationId
- 不同的问题内容
- 不同的时间戳
- 寻找签名规律

### 4. 逆向工程

如果找到 v2 签名代码：
1. 记录确切算法
2. 提取关键参数
3. 验证算法正确性
4. 更新 SDK 实现

## 技术建议

### 临时解决方案

在找到正确算法之前：
1. 使用现有的错误处理机制
2. 实现本地备用方案
3. 记录详细的调试日志

### 长期解决方案

1. **完整的 v2 支持**
   - 实现正确的 v2 签名算法
   - 版本自动检测
   - 统一的接口封装

2. **监控和维护**
   - 持续监控算法变化
   - 自动化测试验证
   - 文档同步更新

## 结论

v2 接口确实使用了与 v1 不同的签名算法，需要通过浏览器调试找到具体的实现逻辑。我们已经排除了大部分常见的算法变体，重点应该放在：

1. **查找专门的 v2 处理代码**
2. **理解流式响应的特殊要求**  
3. **发现可能的服务器端密钥或动态参数**

建议立即开始浏览器调试，使用我们提供的调试指南来定位 v2 签名算法的确切实现。
