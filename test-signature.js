/**
 * 测试签名生成算法
 * 对比我们的实现与官方文档中的示例
 */

const crypto = require('crypto');

// 从文档与 _app chunk 反推的示例数据（补充 method 与 url）
const testCases = [
  {
    name: "generateId API 示例",
    method: 'POST',
    url: '/ai-search/commonApi/v1/generateId',
    timestamp: 1755239241, // 秒级时间戳
    nonce: "-un-m0ntXQIf0i-byz12f",
    deviceId: "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
    data: { timestamp: 1755239241456 }, // 请求体（毫秒级）
    expectedSign: "03A7FFE5DCFB0AC2C486A05ED8198142" // 若与新规则不符，仅用于对照
  },
  {
    name: "createConversation API 示例",
    method: 'POST',
    url: '/ai-search/conversationApi/v1/batch/create',
    timestamp: 1755239240,
    nonce: "OOZpusZl8ANtYIAXqUkgP",
    deviceId: "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
    data: {
      conversationList: [{
        metaData: {
          chatModelConfig: {},
          superAgentPath: "/chat"
        },
        shareId: "",
        isAnonymous: false,
        source: ""
      }]
    },
    expectedSign: "0D2DA56E3D33440213FCC5B1326C959B"
  }
];

/**
 * 按 _app 规则规范化：
 * - GET: 取 URL 中 ? 之后的查询字符串
 * - POST: 使用原始 body 字符串
 */
function normalizeForSign(method, url, bodyRawOrData) {
  const m = (method || 'POST').toUpperCase();
  if (m === 'GET') {
    // 仅取 querystring 原样
    const qIndex = url.indexOf('?');
    return qIndex !== -1 ? url.substring(qIndex + 1) : '';
  }
  if (m === 'POST') {
    if (typeof bodyRawOrData === 'string') return bodyRawOrData;
    if (bodyRawOrData && typeof bodyRawOrData === 'object') {
      // 兜底：序列化为 JSON 字符串（注意：线上以“实际发送体原文”为准）
      return JSON.stringify(bodyRawOrData);
    }
    return '';
  }
  return '';
}

/**
 * 新规则：sign = MD5( timestamp + normalized + nonce ).toUpperCase()
 */
function generateSignatureAppRule({ timestamp, nonce, method, url, data }) {
  const normalized = normalizeForSign(method, url || '', data);
  const baseString = `${timestamp}${normalized}${nonce}`;
  const sign = crypto.createHash('md5').update(baseString).digest('hex').toUpperCase();
  return { normalized, baseString, sign };
}

/**
 * 旧规则（保留用于对照）：按键名排序的 kv 串
 */
function generateSignatureLegacy(params) {
  const signParams = {
    timestamp: params.timestamp,
    nonce: params.nonce,
    deviceId: params.deviceId,
  };
  if (params.data) {
    signParams['data'] = JSON.stringify(params.data);
  }
  const sortedKeys = Object.keys(signParams).sort();
  const signString = sortedKeys.map(key => `${key}=${signParams[key]}`).join('&');
  const sign = crypto.createHash('md5').update(signString).digest('hex').toUpperCase();
  return { signString, sign };
}

/**
 * 测试不同的签名算法变体
 */
function testSignatureVariants(testCase) {
  console.log(`\n=== 测试 ${testCase.name} ===`);
  if (testCase.expectedSign) {
    console.log('期望签名:', testCase.expectedSign);
  }

  // 变体A：_app 规则（目标规则）
  const appRule = generateSignatureAppRule(testCase);
  console.log('—— 目标规则（_app）：');
  console.log('规范化串(normalized) 前200字节:', (appRule.normalized || '').substring(0, 200));
  console.log('签名基串(baseString) 前200字节:', appRule.baseString.substring(0, 200));
  console.log('sign:', appRule.sign);
  if (testCase.expectedSign) {
    console.log('匹配(目标):', appRule.sign === testCase.expectedSign);
  }

  // 变体B：旧实现（仅对照）
  const legacy = generateSignatureLegacy(testCase);
  console.log('\n—— 旧规则（对照）：');
  console.log('kv 串:', legacy.signString.substring(0, 200));
  console.log('sign:', legacy.sign);
  if (testCase.expectedSign) {
    console.log('匹配(旧):', legacy.sign === testCase.expectedSign);
  }

  // 其他少量对照变体可按需保留
  const signStringNoSep = `${testCase.timestamp}${testCase.nonce}${testCase.deviceId}`;
  const signNoSep = crypto.createHash('md5').update(signStringNoSep).digest('hex').toUpperCase();
  console.log('\n—— 对照（无分隔符 timestamp+nonce+deviceId）:');
  console.log('sign:', signNoSep);
}

// 运行测试
console.log('开始测试签名生成算法...\n');

testCases.forEach(testCase => {
  testSignatureVariants(testCase);
});

console.log('\n=== 测试完成 ===');
