# 开发指南

## 项目结构

```
dangbei-provider/
├── src/                    # 源代码目录
│   ├── types/             # 类型定义
│   │   ├── common.ts      # 通用类型
│   │   ├── conversation.ts # 对话相关类型
│   │   ├── chat.ts        # 聊天相关类型
│   │   └── index.ts       # 类型导出
│   ├── utils/             # 工具类
│   │   ├── signature.ts   # 签名生成工具
│   │   ├── device.ts      # 设备管理工具
│   │   └── index.ts       # 工具导出
│   ├── services/          # 服务层
│   │   ├── http-client.ts # HTTP客户端
│   │   ├── sse-client.ts  # SSE客户端
│   │   ├── conversation-service.ts # 对话服务
│   │   ├── common-service.ts # 通用服务
│   │   ├── chat-service.ts # 聊天服务
│   │   └── index.ts       # 服务导出
│   ├── providers/         # Provider层
│   │   ├── dangbei-provider.ts # 主Provider类
│   │   └── index.ts       # Provider导出
│   └── index.ts           # 主入口文件
├── tests/                 # 测试目录
│   ├── unit/             # 单元测试
│   ├── integration/      # 集成测试
│   └── setup.ts          # 测试设置
├── docs/                 # 文档目录
├── examples/             # 示例代码
└── dist/                 # 构建输出目录
```

## 开发环境设置

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式运行

```bash
npm run dev
```

### 3. 构建项目

```bash
npm run build
```

### 4. 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并监听变化
npm run test:watch

# 运行特定测试文件
npm test -- tests/unit/utils.test.ts
```

### 5. 代码检查

```bash
# 检查代码风格
npm run lint

# 自动修复代码风格问题
npm run lint:fix
```

## 架构设计

### 分层架构

1. **Provider层** - 提供统一的高级API接口
2. **Service层** - 实现具体的业务逻辑
3. **Utils层** - 提供通用工具函数
4. **Types层** - 定义所有类型接口

### 核心组件

#### HttpClient
- 负责HTTP请求的发送和响应处理
- 自动添加签名和标准请求头
- 实现重试机制和错误处理

#### SSEClient
- 处理Server-Sent Events流式响应
- 解析不同类型的SSE消息
- 提供回调机制处理实时数据

#### SignatureUtils
- 生成API请求所需的MD5签名
- 提供nonce生成和时间戳获取功能
- 支持签名验证

#### DeviceUtils
- 生成和管理设备标识符
- 创建标准设备配置
- 生成标准HTTP请求头

## API调用流程

### 1. 创建对话流程

```
用户调用 → DangbeiProvider.createConversation()
         ↓
         ConversationService.createConversation()
         ↓
         HttpClient.post('/ai-search/conversationApi/v1/batch/create')
         ↓
         自动添加签名和请求头
         ↓
         发送HTTP请求
         ↓
         返回对话信息
```

### 2. 聊天消息流程

```
用户调用 → DangbeiProvider.chat()
         ↓
         生成消息ID (CommonService.generateId())
         ↓
         构建聊天请求参数
         ↓
         ChatService.sendChatRequest()
         ↓
         建立SSE连接
         ↓
         接收流式响应
         ↓
         通过回调返回消息片段
         ↓
         聊天完成
```

### 3. 签名生成流程

```
HTTP请求 → 请求拦截器
          ↓
          生成timestamp和nonce
          ↓
          SignatureUtils.generateSignature()
          ↓
          构建签名字符串 (timestamp=xxx&nonce=xxx&deviceId=xxx)
          ↓
          MD5哈希并转大写
          ↓
          添加到请求头
```

## 错误处理策略

### 错误类型

1. **网络错误** - 连接失败、超时等
2. **API错误** - 服务器返回的业务错误
3. **参数错误** - 客户端参数验证失败
4. **签名错误** - 签名生成或验证失败
5. **未知错误** - 其他未分类错误

### 错误处理机制

1. **自动重试** - 网络错误和超时错误自动重试
2. **错误分类** - 根据错误类型提供不同的处理策略
3. **错误上下文** - 保留请求ID、错误代码等上下文信息
4. **优雅降级** - 服务不可用时提供本地备用方案

## 测试策略

### 单元测试

- 测试工具函数的正确性
- 验证类型定义的完整性
- 模拟外部依赖进行隔离测试

### 集成测试

- 测试完整的API调用流程
- 验证错误处理机制
- 测试并发和性能场景

### 测试覆盖率

目标覆盖率：
- 语句覆盖率 > 90%
- 分支覆盖率 > 85%
- 函数覆盖率 > 95%

## 性能优化

### 1. 连接复用

- HTTP客户端使用连接池
- 避免频繁创建新连接

### 2. 请求优化

- 合理设置超时时间
- 实现智能重试策略
- 压缩请求和响应数据

### 3. 内存管理

- 及时清理SSE连接
- 避免内存泄漏
- 合理使用缓存

## 安全考虑

### 1. 签名安全

- 使用时间戳防止重放攻击
- 随机nonce增加签名复杂度
- 签名算法不可逆

### 2. 数据安全

- 敏感信息不记录日志
- 传输过程使用HTTPS
- 验证输入参数防止注入

### 3. 设备标识

- 设备ID生成算法安全
- 避免设备指纹泄露
- 支持设备ID轮换

## 发布流程

### 1. 版本管理

- 遵循语义化版本规范
- 维护详细的变更日志
- 标记重要的里程碑版本

### 2. 构建流程

```bash
# 1. 清理旧构建
npm run clean

# 2. 运行测试
npm test

# 3. 代码检查
npm run lint

# 4. 构建项目
npm run build

# 5. 发布到npm
npm publish
```

### 3. 文档更新

- 更新API文档
- 更新使用示例
- 更新变更日志

## 贡献指南

### 1. 代码规范

- 使用TypeScript严格模式
- 遵循ESLint配置规则
- 添加详细的中文注释

### 2. 提交规范

- 使用语义化提交信息
- 每个提交只包含一个功能
- 提供清晰的提交描述

### 3. Pull Request

- 创建功能分支进行开发
- 确保所有测试通过
- 更新相关文档

## 常见问题

### Q: 如何调试API调用？

A: 启用debug模式：
```typescript
const provider = new DangbeiProvider({ debug: true });
```

### Q: 如何处理网络超时？

A: 调整超时设置：
```typescript
const provider = new DangbeiProvider({ 
  timeout: 60000,  // 60秒
  retries: 5       // 重试5次
});
```

### Q: 如何自定义设备配置？

A: 提供自定义配置：
```typescript
const provider = new DangbeiProvider({
  deviceConfig: {
    deviceId: 'your-custom-device-id',
    lang: 'en'
  }
});
```
