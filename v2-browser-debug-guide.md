# v2 接口签名算法浏览器调试指南

## 分析结论

经过详尽的逆向分析，我们确认：
- **v1 接口**：使用标准的 `MD5(timestamp + body + nonce).toUpperCase()` 算法 ✅
- **v2 接口**：使用了完全不同的签名算法 ❌

## 浏览器调试步骤

### 1. 查找版本判断逻辑

在浏览器开发者工具的 Sources 面板中搜索以下关键词：

```javascript
// 搜索版本判断相关代码
"v2"
"/v2/"
"chatApi"
"version"
"chat"

// 搜索可能的条件判断
"if.*v2"
"includes.*v2"
"indexOf.*v2"
"match.*v2"
```

### 2. 查找签名生成函数

搜索可能的签名生成相关代码：

```javascript
// 签名生成函数
"generateSign"
"createSign"
"getSign"
"sign.*="
"MD5"
"crypto"
"hash"

// 可能的函数名
"O("
"S("
"normaliz"
"timestamp.*nonce"
```

### 3. 设置断点策略

#### 方法1：网络拦截断点
```javascript
// 在 Console 中执行，拦截所有包含 v2 的请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && url.includes('/v2/')) {
    console.log('🔍 v2 请求拦截:', url);
    debugger; // 这里会触发断点
  }
  return originalFetch.apply(this, args);
};

// 同样拦截 XMLHttpRequest
const originalOpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url) {
  if (url.includes('/v2/')) {
    console.log('🔍 v2 XHR 请求拦截:', url);
    debugger;
  }
  return originalOpen.apply(this, arguments);
};
```

#### 方法2：签名生成断点
```javascript
// 拦截可能的签名生成
const originalCreateHash = crypto.createHash;
if (typeof crypto !== 'undefined' && crypto.createHash) {
  crypto.createHash = function(algorithm) {
    console.log('🔍 Hash 算法调用:', algorithm);
    console.trace(); // 打印调用栈
    debugger;
    return originalCreateHash.apply(this, arguments);
  };
}
```

### 4. 查找特定的代码模式

#### 查找可能的 v2 特殊处理
```javascript
// 在代码中搜索这些模式
case.*v2
switch.*version
if.*chatApi
url.*v2.*chat
method.*POST.*chat
```

#### 查找可能的签名差异
```javascript
// 可能的 v2 特殊签名逻辑
timestamp.*chat
nonce.*stream
body.*v2
conversationId.*sign
```

### 5. 监控请求头生成

在 Network 面板中：

1. **清空网络记录**
2. **发起 v2 聊天请求**
3. **查看请求详情**，特别关注：
   - `sign` 头的值
   - `timestamp` 和 `nonce` 的值
   - 请求体的确切内容

### 6. 对比 v1 和 v2 请求

创建对比表格：

| 项目 | v1 接口 | v2 接口 |
|------|---------|---------|
| URL | `/ai-search/conversationApi/v1/batch/create` | `/ai-search/chatApi/v2/chat` |
| 签名算法 | `MD5(timestamp + body + nonce)` | **未知** |
| 请求体大小 | 较小 (128字符) | 较大 (415字符) |
| 特殊字段 | 无 | `stream: true` |

### 7. 可能的 v2 签名规则假设

基于分析，v2 可能使用以下规则之一：

1. **不同的参数组合**
   ```javascript
   // 可能不包含完整 body
   MD5(timestamp + conversationId + nonce)
   MD5(timestamp + question + nonce)
   ```

2. **特殊的 body 处理**
   ```javascript
   // 可能只使用 body 的特定字段
   MD5(timestamp + extractedFields + nonce)
   ```

3. **版本相关的密钥**
   ```javascript
   // 可能使用 HMAC 或特殊密钥
   HMAC_MD5(key, timestamp + body + nonce)
   ```

4. **完全不同的算法**
   ```javascript
   // 可能使用其他哈希算法或自定义算法
   SHA256(timestamp + body + nonce)
   CustomHash(timestamp + body + nonce)
   ```

### 8. 调试技巧

#### 使用条件断点
```javascript
// 在可能的签名生成函数中设置条件断点
// 条件：url.includes('/v2/') || url.includes('chat')
```

#### 监控变量变化
```javascript
// 监控关键变量
console.log('签名输入参数:', {
  timestamp: timestamp,
  nonce: nonce,
  url: url,
  method: method,
  body: body
});
```

### 9. 预期发现

您应该能找到类似这样的代码：

```javascript
// 可能的 v2 特殊处理
if (url.includes('/v2/') || url.includes('chatApi')) {
  // v2 特殊签名逻辑
  sign = generateV2Sign(timestamp, nonce, specialParams);
} else {
  // v1 标准签名逻辑
  sign = MD5(timestamp + normalized + nonce).toUpperCase();
}
```

### 10. 下一步行动

1. **找到 v2 签名代码后**，记录确切的算法
2. **更新我们的 SDK** 以支持 v2 签名
3. **验证算法正确性**
4. **文档化差异**

## 重要提示

- v2 接口的签名算法可能更复杂或使用了服务器端密钥
- 可能需要特殊的参数提取或处理逻辑
- 注意观察是否有动态生成的参数或时间窗口限制
