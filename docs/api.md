# API 文档

## 概述

当贝AI Provider SDK 提供了完整的当贝AI API调用功能，包括对话创建、消息发送、流式响应处理等。

## 核心类

### DangbeiProvider

主要的Provider类，整合了所有API功能。

#### 构造函数

```typescript
constructor(options?: DangbeiProviderOptions)
```

**参数说明:**

- `options.deviceConfig?: Partial<DeviceConfig>` - 设备配置
- `options.timeout?: number` - 请求超时时间（毫秒），默认30000
- `options.retries?: number` - 重试次数，默认3
- `options.debug?: boolean` - 是否启用调试日志，默认false

#### 方法

##### createConversation(options?)

创建新的对话会话。

```typescript
async createConversation(options?: {
  superAgentPath?: string;
  isAnonymous?: boolean;
  source?: string;
}): Promise<CreateConversationResponse>
```

**返回值:**
- `CreateConversationResponse` - 包含对话ID、标题等信息的对话对象

##### chat(options)

发送聊天消息并接收流式响应。

```typescript
async chat(options: ChatOptions): Promise<ChatResponse>
```

**参数:**
- `options.conversationId: string` - 对话ID（必需）
- `options.question: string` - 问题内容（必需）
- `options.model?: string` - 模型名称，默认'doubao-1_6-thinking'
- `options.botCode?: string` - 机器人代码，默认'AI_SEARCH'
- `options.chatOption?: Partial<ChatOption>` - 聊天选项配置
- `options.callbacks?: ChatCallbacks` - 回调函数

**返回值:**
- `ChatResponse` - 包含完整响应内容和元数据

##### chatSync(options)

发送聊天消息并等待完整响应。

```typescript
async chatSync(options: ChatOptions): Promise<string>
```

**返回值:**
- `string` - 完整的响应内容

##### quickChat(question, callbacks?)

快速聊天，自动创建对话并发送消息。

```typescript
async quickChat(
  question: string, 
  callbacks?: ChatCallbacks
): Promise<ChatResponse>
```

##### generateId(timestamp?)

生成唯一ID。

```typescript
async generateId(timestamp?: number): Promise<string>
```

##### stopChat()

停止当前聊天。

```typescript
stopChat(): void
```

##### getDeviceConfig()

获取当前设备配置。

```typescript
getDeviceConfig(): DeviceConfig
```

##### updateDeviceConfig(config)

更新设备配置。

```typescript
updateDeviceConfig(config: Partial<DeviceConfig>): void
```

##### getStatus()

获取服务状态。

```typescript
getStatus(): {
  deviceId: string;
  chatStatus: {
    isConnected: boolean;
    readyState: string;
  };
  serviceAvailable: boolean;
}
```

##### checkServiceAvailability()

检查服务可用性。

```typescript
async checkServiceAvailability(): Promise<boolean>
```

##### destroy()

销毁Provider实例，清理资源。

```typescript
destroy(): void
```

## 类型定义

### DeviceConfig

设备配置接口。

```typescript
interface DeviceConfig {
  deviceId: string;      // 设备唯一标识符
  appType: number;       // 应用类型，固定为6
  appVersion: string;    // 应用版本号
  clientVersion: string; // 客户端版本号
  lang: string;          // 语言设置
  userAgent: string;     // 用户代理字符串
}
```

### ChatOptions

聊天配置选项。

```typescript
interface ChatOptions {
  conversationId: string;           // 对话ID
  question: string;                 // 问题内容
  model?: string;                   // 模型名称
  botCode?: string;                 // 机器人代码
  chatOption?: Partial<ChatOption>; // 聊天选项
  callbacks?: ChatCallbacks;        // 回调函数
}
```

### ChatCallbacks

聊天回调函数接口。

```typescript
interface ChatCallbacks {
  onMessage?: (content: string, data: SSEMessageDelta) => void;
  onComplete?: (data: SSEChatCompleted) => void;
  onError?: (error: Error) => void;
}
```

### ChatResponse

聊天响应接口。

```typescript
interface ChatResponse {
  content: string;        // 完整响应内容
  messageId: string;      // 消息ID
  parentMessageId: string; // 父消息ID
  conversationId: string; // 对话ID
  requestId: string;      // 请求ID
}
```

### DangbeiApiError

自定义错误类。

```typescript
class DangbeiApiError extends Error {
  readonly type: ErrorType;
  readonly code?: string;
  readonly requestId?: string;
}
```

### ErrorType

错误类型枚举。

```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  SIGNATURE_ERROR = 'SIGNATURE_ERROR',
  PARAMETER_ERROR = 'PARAMETER_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
```

## 底层服务

### HttpClient

HTTP请求客户端，处理签名、重试等。

### ConversationService

对话管理服务。

### ChatService

聊天服务，处理消息发送和SSE响应。

### CommonService

通用服务，提供ID生成等功能。

### SSEClient

Server-Sent Events客户端，处理流式响应。

## 工具类

### SignatureUtils

签名生成和验证工具。

### DeviceUtils

设备ID生成和配置管理工具。
