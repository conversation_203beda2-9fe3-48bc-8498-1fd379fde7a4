{"name": "dangbei-provider", "version": "1.0.0", "description": "当贝AI API调用SDK - 提供完整的当贝AI对话接口封装", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "compile-wasm": "node scripts/compile-wasm.js sign_bg.wat sign_bg.wasm", "test-wasm": "node examples/wasm-signature-usage.js", "test-wasm-only": "node -e \"const DangbeiSignatureWasm = require('./src/wasm/dangbei-signature-wasm'); console.log('WASM 模块测试完成')\"", "test-fallback": "node -e \"const { FallbackSignature } = require('./src/wasm/fallback-signature'); const signer = new FallbackSignature(); console.log('备用签名:', signer.generateSignature('test', 'nonce'))\"", "test-unified": "node -e \"const { quickSign } = require('./src/wasm/unified-signature'); quickSign('test', 'nonce').then(sig => console.log('统一签名:', sig)).catch(console.error)\"", "benchmark": "node -e \"const { UnifiedSignature } = require('./src/wasm/unified-signature'); const signer = new UnifiedSignature(); signer.initialize().then(() => signer.performanceTest(100)).then(results => console.log('性能测试:', results)).catch(console.error)\"", "debug-wasm": "NODE_ENV=development node examples/wasm-signature-usage.js", "setup-wasm": "npm run compile-wasm && npm run test-wasm"}, "keywords": ["dangbei", "ai", "chat", "api", "sdk", "typescript", "webassembly", "wasm", "signature", "crypto"], "author": "AIER Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "eventsource": "^2.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/eventsource": "^1.1.15", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "optionalDependencies": {"wabt": "^1.0.24"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+https://git.atjog.com/aier/dangbei-provider.git"}, "bugs": {"url": "https://git.atjog.com/aier/dangbei-provider/issues"}, "homepage": "https://git.atjog.com/aier/dangbei-provider#readme"}