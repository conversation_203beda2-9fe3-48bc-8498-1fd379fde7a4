/**
 * 高级签名算法逆向分析
 * 尝试更多可能的签名生成方式
 */

const crypto = require('crypto');

// 真实请求参数
const realRequest = {
  timestamp: 1755252943,
  nonce: '111g0amuaFUAic500cMI-',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '9CC214FB53DDAF31DC1BFE453D14C468',
  requestBody: {
    conversationList: [{
      metaData: {
        chatModelConfig: {},
        superAgentPath: "/chat"
      },
      shareId: "",
      isAnonymous: false,
      source: ""
    }]
  }
};

console.log('=== 高级签名算法逆向分析 ===\n');

// 测试签名生成函数
function testSignature(description, signString, expectedSign) {
  console.log(`\n--- ${description} ---`);
  console.log(`签名字符串: ${signString}`);
  
  const algorithms = ['md5', 'sha1', 'sha256'];
  
  for (const algo of algorithms) {
    const hash = crypto.createHash(algo).update(signString).digest('hex').toUpperCase();
    console.log(`${algo.toUpperCase()}: ${hash}`);
    
    if (hash === expectedSign) {
      console.log(`🎉 匹配! 算法: ${algo.toUpperCase()}`);
      return true;
    }
  }
  
  console.log('❌ 无匹配');
  return false;
}

// 可能的密钥列表
const possibleKeys = [
  'dangbei',
  'ai',
  'api',
  'secret',
  'key',
  'salt',
  'dangbei-ai',
  'ai-api',
  'dangbei_ai',
  'DANGBEI',
  'AI',
  'SECRET',
  '123456',
  'abc123',
  'dangbei123',
  'ai123'
];

// 1. 测试带密钥的HMAC算法
console.log('1. 测试HMAC算法:');
const baseString = `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`;

for (const key of possibleKeys) {
  const hmacMd5 = crypto.createHmac('md5', key).update(baseString).digest('hex').toUpperCase();
  const hmacSha1 = crypto.createHmac('sha1', key).update(baseString).digest('hex').toUpperCase();
  
  console.log(`密钥 "${key}": MD5=${hmacMd5.substring(0, 8)}... SHA1=${hmacSha1.substring(0, 8)}...`);
  
  if (hmacMd5 === realRequest.sign) {
    console.log(`🎉 找到匹配! HMAC-MD5 密钥: "${key}"`);
    return;
  }
  if (hmacSha1 === realRequest.sign) {
    console.log(`🎉 找到匹配! HMAC-SHA1 密钥: "${key}"`);
    return;
  }
}

// 2. 测试不同的参数组合顺序
console.log('\n2. 测试不同参数组合:');

const paramCombinations = [
  // 只包含核心参数
  `${realRequest.timestamp}${realRequest.nonce}${realRequest.deviceId}`,
  `${realRequest.deviceId}${realRequest.timestamp}${realRequest.nonce}`,
  `${realRequest.nonce}${realRequest.timestamp}${realRequest.deviceId}`,
  
  // 带等号但无&分隔符
  `timestamp=${realRequest.timestamp}nonce=${realRequest.nonce}deviceId=${realRequest.deviceId}`,
  `deviceId=${realRequest.deviceId}timestamp=${realRequest.timestamp}nonce=${realRequest.nonce}`,
  
  // 不同分隔符
  `timestamp:${realRequest.timestamp},nonce:${realRequest.nonce},deviceId:${realRequest.deviceId}`,
  `${realRequest.timestamp}_${realRequest.nonce}_${realRequest.deviceId}`,
  `${realRequest.timestamp}#${realRequest.nonce}#${realRequest.deviceId}`,
];

for (let i = 0; i < paramCombinations.length; i++) {
  if (testSignature(`参数组合 ${i + 1}`, paramCombinations[i], realRequest.sign)) {
    return;
  }
}

// 3. 测试包含额外参数
console.log('\n3. 测试包含额外参数:');

const extraParams = [
  // 可能的固定参数
  `appType=6&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  `appVersion=1.1.18&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  `client-ver=1.0.2&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  `lang=zh&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  
  // 组合多个参数
  `appType=6&appVersion=1.1.18&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  `appType=6&client-ver=1.0.2&deviceId=${realRequest.deviceId}&lang=zh&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
];

for (let i = 0; i < extraParams.length; i++) {
  if (testSignature(`额外参数 ${i + 1}`, extraParams[i], realRequest.sign)) {
    return;
  }
}

// 4. 测试Base64编码
console.log('\n4. 测试Base64编码:');

const base64Tests = [
  Buffer.from(baseString).toString('base64'),
  Buffer.from(`${realRequest.timestamp}${realRequest.nonce}${realRequest.deviceId}`).toString('base64'),
];

for (let i = 0; i < base64Tests.length; i++) {
  if (testSignature(`Base64编码 ${i + 1}`, base64Tests[i], realRequest.sign)) {
    return;
  }
}

// 5. 测试URL路径包含
console.log('\n5. 测试包含URL路径:');

const urlPath = '/ai-search/conversationApi/v1/batch/create';
const urlTests = [
  `${urlPath}&deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&timestamp=${realRequest.timestamp}`,
  `deviceId=${realRequest.deviceId}&nonce=${realRequest.nonce}&path=${urlPath}&timestamp=${realRequest.timestamp}`,
  `${realRequest.timestamp}${realRequest.nonce}${realRequest.deviceId}${urlPath}`,
];

for (let i = 0; i < urlTests.length; i++) {
  if (testSignature(`URL路径 ${i + 1}`, urlTests[i], realRequest.sign)) {
    return;
  }
}

// 6. 测试双重哈希
console.log('\n6. 测试双重哈希:');

const singleHash = crypto.createHash('md5').update(baseString).digest('hex');
const doubleHash = crypto.createHash('md5').update(singleHash).digest('hex').toUpperCase();

console.log(`单次哈希: ${singleHash}`);
console.log(`双重哈希: ${doubleHash}`);

if (doubleHash === realRequest.sign) {
  console.log('🎉 找到匹配! 双重MD5哈希');
  return;
}

console.log('\n=== 分析完成 - 未找到匹配的算法 ===');
console.log('可能的原因:');
console.log('1. 使用了未知的密钥或盐值');
console.log('2. 包含了我们未考虑的参数');
console.log('3. 使用了特殊的编码或变换');
console.log('4. 签名算法可能不是标准的哈希算法');
console.log('5. 可能需要特定的字符编码（如UTF-8、GBK等）');
