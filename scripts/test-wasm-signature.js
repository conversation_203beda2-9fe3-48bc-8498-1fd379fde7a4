#!/usr/bin/env node

/**
 * WASM签名算法测试脚本
 * 用于验证我们的WASM模拟器实现是否正确
 */

const { WasmSignatureEmulator } = require('../dist/utils/wasm-signature-emulator');
const { SignatureV2Utils } = require('../dist/utils/signature-v2');

// 真实的测试数据（来自调用流程.md和分析文档）
const realTestCases = [
  {
    name: '真实聊天请求 #1',
    timestamp: 1755239241,
    nonce: 'QL4MKOwQFtSmnhCOmNjde',
    deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
    expectedSign: '460D94E7C6980A6973494BC75D075905',
    requestData: '{"stream":true,"botCode":"AI_SEARCH","conversationId":"363022964585267589","question":"你好!","model":"doubao-1_6-thinking","chatOption":{"searchKnowledge":false,"searchAllKnowledge":false,"searchSharedKnowledge":false},"knowledgeList":[],"anonymousKey":"","uuid":"363022965811450053","chatId":"363022965811450053","files":[],"reference":[],"role":"user","status":"local","content":"你好!","userAction":"","agentId":""}',
    method: 'POST',
    url: '/ai-search/chatApi/v2/chat'
  },
  {
    name: '高级分析请求',
    timestamp: 1755252943,
    nonce: '111g0amuaFUAic500cMI-',
    deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
    expectedSign: '9CC214FB53DDAF31DC1BFE453D14C468',
    requestData: JSON.stringify({
      conversationList: [{
        metaData: {
          chatModelConfig: {},
          superAgentPath: "/chat"
        },
        shareId: "",
        isAnonymous: false,
        source: ""
      }]
    }),
    method: 'POST',
    url: '/ai-search/chatApi/v2/chat'
  },
  {
    name: 'generateId API',
    timestamp: 1755239241,
    nonce: '-un-m0ntXQIf0i-byz12f',
    deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
    expectedSign: '03A7FFE5DCFB0AC2C486A05ED8198142',
    requestData: JSON.stringify({ timestamp: 1755239241456 }),
    method: 'POST',
    url: '/ai-search/commonApi/v1/generateId'
  }
];

/**
 * 测试单个策略
 */
function testStrategy(strategy, testCase) {
  console.log(`\n🧪 测试策略: ${strategy.toUpperCase()}`);
  
  try {
    const emulator = new WasmSignatureEmulator({
      debug: true,
      strategy: strategy,
      secretKey: `dangbei_ai_${strategy}_2024`
    });
    
    const timestampNonce = `${testCase.timestamp}:${testCase.nonce}`;
    const result = emulator.getSign(testCase.requestData, timestampNonce);
    
    console.log(`   生成签名: ${result.signature}`);
    console.log(`   期望签名: ${testCase.expectedSign}`);
    console.log(`   匹配结果: ${result.signature === testCase.expectedSign ? '✅ 匹配' : '❌ 不匹配'}`);
    
    if (result.debug) {
      console.log(`   算法: ${result.debug.algorithm}`);
      console.log(`   数据长度: ${result.debug.normalizedData.length} 字符`);
    }
    
    return result.signature === testCase.expectedSign;
    
  } catch (error) {
    console.error(`   ❌ 策略失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试V2签名工具
 */
function testV2Utils(testCase) {
  console.log(`\n🔧 测试V2签名工具`);
  
  try {
    const params = {
      timestamp: testCase.timestamp,
      nonce: testCase.nonce,
      deviceId: testCase.deviceId,
      method: testCase.method,
      url: testCase.url,
      bodyRaw: testCase.requestData
    };
    
    const signature = SignatureV2Utils.generateV2Signature(params);
    
    console.log(`   生成签名: ${signature}`);
    console.log(`   期望签名: ${testCase.expectedSign}`);
    console.log(`   匹配结果: ${signature === testCase.expectedSign ? '✅ 匹配' : '❌ 不匹配'}`);
    
    return signature === testCase.expectedSign;
    
  } catch (error) {
    console.error(`   ❌ V2工具失败: ${error.message}`);
    return false;
  }
}

/**
 * 尝试不同的密钥
 */
function testDifferentKeys(testCase) {
  console.log(`\n🔑 测试不同密钥`);
  
  const keys = [
    'dangbei_ai_secret_2024',
    'dangbei_ai_v2_secret_2024',
    'dangbei',
    'ai',
    'secret',
    'dangbei_secret',
    'ai_secret',
    'wasm_secret',
    '',  // 空密钥
    'default_key'
  ];
  
  for (const key of keys) {
    try {
      const emulator = new WasmSignatureEmulator({
        debug: false,
        strategy: 'hybrid',
        secretKey: key
      });
      
      const timestampNonce = `${testCase.timestamp}:${testCase.nonce}`;
      const result = emulator.getSign(testCase.requestData, timestampNonce);
      
      if (result.signature === testCase.expectedSign) {
        console.log(`   ✅ 密钥匹配: "${key}" -> ${result.signature}`);
        return key;
      } else {
        console.log(`   ❌ 密钥不匹配: "${key}" -> ${result.signature}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 密钥错误: "${key}" -> ${error.message}`);
    }
  }
  
  return null;
}

/**
 * 尝试不同的时间处理方式
 */
function testTimeProcessing(testCase) {
  console.log(`\n⏰ 测试时间处理`);
  
  const timeOffsets = [0, 1000, -1000, 3600000, -3600000]; // 不同的时间偏移
  
  for (const offset of timeOffsets) {
    try {
      const emulator = new WasmSignatureEmulator({
        debug: false,
        strategy: 'hybrid',
        timeOffset: offset
      });
      
      const timestampNonce = `${testCase.timestamp}:${testCase.nonce}`;
      const result = emulator.getSign(testCase.requestData, timestampNonce);
      
      if (result.signature === testCase.expectedSign) {
        console.log(`   ✅ 时间偏移匹配: ${offset}ms -> ${result.signature}`);
        return offset;
      } else {
        console.log(`   ❌ 时间偏移不匹配: ${offset}ms -> ${result.signature}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 时间偏移错误: ${offset}ms -> ${error.message}`);
    }
  }
  
  return null;
}

/**
 * 主测试函数
 */
function runTests() {
  console.log('🎯 开始WASM签名算法测试\n');
  console.log('=' .repeat(80));
  
  let totalTests = 0;
  let passedTests = 0;
  
  for (const testCase of realTestCases) {
    console.log(`\n📋 测试用例: ${testCase.name}`);
    console.log(`📝 时间戳: ${testCase.timestamp}`);
    console.log(`📝 Nonce: ${testCase.nonce}`);
    console.log(`📝 设备ID: ${testCase.deviceId.substring(0, 20)}...`);
    console.log(`📝 期望签名: ${testCase.expectedSign}`);
    console.log('─'.repeat(60));
    
    // 测试所有策略
    const strategies = ['standard', 'enhanced', 'hybrid'];
    for (const strategy of strategies) {
      totalTests++;
      if (testStrategy(strategy, testCase)) {
        passedTests++;
      }
    }
    
    // 测试V2工具
    totalTests++;
    if (testV2Utils(testCase)) {
      passedTests++;
    }
    
    // 尝试不同的密钥
    const matchedKey = testDifferentKeys(testCase);
    if (matchedKey !== null) {
      console.log(`🎉 找到匹配的密钥: "${matchedKey}"`);
    }
    
    // 尝试不同的时间处理
    const matchedOffset = testTimeProcessing(testCase);
    if (matchedOffset !== null) {
      console.log(`🎉 找到匹配的时间偏移: ${matchedOffset}ms`);
    }
    
    console.log('═'.repeat(80));
  }
  
  // 输出测试结果
  console.log(`\n📊 测试结果统计:`);
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   通过测试: ${passedTests}`);
  console.log(`   失败测试: ${totalTests - passedTests}`);
  console.log(`   通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === 0) {
    console.log(`\n❌ 所有测试都失败了，这表明我们的WASM模拟器还需要进一步优化`);
    console.log(`💡 建议:`);
    console.log(`   1. 检查时间戳处理逻辑`);
    console.log(`   2. 尝试不同的哈希算法组合`);
    console.log(`   3. 分析原始WASM代码的更多细节`);
    console.log(`   4. 考虑是否有隐藏的密钥或常量`);
  } else if (passedTests < totalTests) {
    console.log(`\n⚠️ 部分测试通过，需要进一步优化算法`);
  } else {
    console.log(`\n🎉 所有测试都通过了！WASM模拟器工作正常`);
  }
}

/**
 * 性能测试
 */
function runPerformanceTest() {
  console.log('\n⚡ 性能测试');
  console.log('─'.repeat(40));
  
  const emulator = new WasmSignatureEmulator({
    debug: false,
    strategy: 'hybrid'
  });
  
  const iterations = 1000;
  const testData = '{"test": "performance data"}';
  const testNonce = '1755239241:perf_nonce';
  
  console.log(`开始 ${iterations} 次迭代...`);
  
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    emulator.getSign(testData, testNonce);
  }
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  const avgTime = duration / iterations;
  
  console.log(`总时间: ${duration}ms`);
  console.log(`平均时间: ${avgTime.toFixed(2)}ms/次`);
  console.log(`吞吐量: ${(1000 / avgTime).toFixed(0)} 次/秒`);
}

// 运行测试
if (require.main === module) {
  try {
    runTests();
    runPerformanceTest();
  } catch (error) {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  }
}
