# 当贝AI WebAssembly 签名模块

## 概述

这个模块提供了在 Node.js 环境中调用当贝AI WebAssembly签名算法的完整解决方案。基于对原始 JavaScript 代码的深入逆向分析，实现了与官方签名算法兼容的多种实现方式。

## 🚀 快速开始

### 1. 编译 WebAssembly 文件

```bash
# 使用内置脚本编译 WAT 文件为 WASM 文件
npm run compile-wasm

# 或者手动编译（需要安装 wabt 工具）
wat2wasm sign_bg.wat -o sign_bg.wasm
```

### 2. 基础使用

```javascript
const { quickSign } = require('./src/wasm/unified-signature');

async function example() {
  try {
    // 快速生成签名
    const signature = await quickSign(
      '{"question":"你好","model":"kimi-k2-0711-preview"}',
      '1755239241:random_nonce_123'
    );
    
    console.log('生成的签名:', signature);
  } catch (error) {
    console.error('签名生成失败:', error.message);
  }
}

example();
```

### 3. 完整示例

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function fullExample() {
  // 创建签名器实例
  const signer = new UnifiedSignature({
    wasmPath: './sign_bg.wasm',
    preferWasm: true,        // 优先使用 WebAssembly
    enableFallback: true,    // 启用 JavaScript 备用方案
    debug: true              // 启用调试日志
  });

  try {
    // 初始化签名器
    await signer.initialize();
    
    // 检查状态
    const status = signer.getStatus();
    console.log('签名器状态:', status);
    
    // 生成签名
    const signature = await signer.generateSignature(
      'request_data',
      'timestamp_nonce'
    );
    
    console.log('生成的签名:', signature);
    
  } catch (error) {
    console.error('操作失败:', error.message);
  } finally {
    // 清理资源
    signer.destroy();
  }
}

fullExample();
```

## 📁 项目结构

```
├── src/wasm/
│   ├── dangbei-signature-wasm.js    # WebAssembly 模块包装器
│   ├── fallback-signature.js        # JavaScript 备用实现
│   └── unified-signature.js         # 统一签名接口
├── examples/
│   └── wasm-signature-usage.js      # 完整使用示例
├── scripts/
│   └── compile-wasm.js              # WASM 编译脚本
├── docs/
│   └── WASM_SIGNATURE_USAGE.md      # 详细使用文档
├── sign_bg.wat                      # WebAssembly 文本格式源文件
└── sign_bg.wasm                     # 编译后的 WebAssembly 二进制文件
```

## 🔧 可用脚本

```bash
# 编译 WebAssembly 文件
npm run compile-wasm

# 运行完整测试示例
npm run test-wasm

# 测试 WebAssembly 模块
npm run test-wasm-only

# 测试备用实现
npm run test-fallback

# 测试统一接口
npm run test-unified

# 性能基准测试
npm run benchmark

# 调试模式运行
npm run debug-wasm

# 完整设置（编译 + 测试）
npm run setup-wasm
```

## 🛠️ 安装要求

### 基础要求
- Node.js >= 16.0.0
- 支持 WebAssembly 的 Node.js 版本

### 可选工具（用于编译 WASM）
- [WABT (WebAssembly Binary Toolkit)](https://github.com/WebAssembly/wabt)
- Docker（用于容器化编译）
- wabt npm 包（JavaScript 实现）

### 安装 WABT 工具

```bash
# macOS (使用 Homebrew)
brew install wabt

# Ubuntu/Debian
sudo apt-get install wabt

# 或者使用 npm 包
npm install wabt --save-dev
```

## 🎯 使用场景

### 1. 模拟当贝AI API调用

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function callDangbeiAPI() {
  const signer = new UnifiedSignature();
  await signer.initialize();

  // 准备API请求数据
  const requestData = {
    stream: true,
    botCode: "AI_SEARCH",
    conversationId: "363022964585267589",
    question: "你好!",
    model: "doubao-1_6-thinking"
  };

  // 生成签名参数
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = generateRandomNonce();
  const requestBody = JSON.stringify(requestData);

  // 生成签名
  const signature = await signer.generateSignature(
    requestBody,
    `${timestamp}:${nonce}`
  );

  // 构建请求头
  const headers = {
    'Content-Type': 'application/json',
    'appType': '6',
    'appVersion': '1.1.17-22',
    'client-ver': '1.0.2',
    'deviceId': 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
    'lang': 'zh',
    'nonce': nonce,
    'sign': signature,
    'timestamp': timestamp.toString()
  };

  console.log('API 请求头:', headers);
  
  // 发送请求（示例）
  // const response = await fetch('https://ai-api.dangbei.net/ai-search/chatApi/v2/chat', {
  //   method: 'POST',
  //   headers,
  //   body: requestBody
  // });

  signer.destroy();
}

function generateRandomNonce() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  let result = '';
  for (let i = 0; i < 17; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
```

### 2. 批量签名处理

```javascript
const { UnifiedSignature } = require('./src/wasm/unified-signature');

async function batchSignature() {
  const signer = new UnifiedSignature();
  await signer.initialize();

  // 准备批量请求
  const requests = [
    { param1: 'data1', param2: 'nonce1' },
    { param1: 'data2', param2: 'nonce2' },
    { param1: 'data3', param2: 'nonce3' }
  ];

  // 批量生成签名
  const signatures = await signer.generateSignatureBatch(requests);
  
  console.log('批量签名结果:', signatures);
  
  signer.destroy();
}
```

## 🔍 故障排除

### 常见问题

1. **WASM 文件编译失败**
   ```bash
   # 检查 wabt 是否安装
   wat2wasm --version
   
   # 或者使用 Docker 编译
   docker run --rm -v $(pwd):/work -w /work wabt/wabt:latest wat2wasm sign_bg.wat -o sign_bg.wasm
   ```

2. **WebAssembly 模块加载失败**
   ```javascript
   // 启用调试模式查看详细错误
   const signer = new UnifiedSignature({ debug: true });
   ```

3. **签名结果不匹配**
   ```javascript
   // 使用备用实现进行对比
   const { FallbackSignature } = require('./src/wasm/fallback-signature');
   const fallback = new FallbackSignature({ debug: true });
   const signature = fallback.generateSignature(param1, param2);
   ```

### 调试技巧

```javascript
// 1. 启用详细日志
process.env.NODE_ENV = 'development';

// 2. 检查签名器状态
const status = signer.getStatus();
console.log('状态:', status);

// 3. 性能分析
const perfResults = await signer.performanceTest(10);
console.log('性能:', perfResults);

// 4. 手动切换实现方式
signer.switchMode('fallback'); // 切换到备用实现
signer.switchMode('wasm');     // 切换到 WebAssembly
```

## 📊 性能特性

- **WebAssembly 模式**: 高性能，接近原生速度
- **备用模式**: 纯 JavaScript 实现，兼容性好
- **自动降级**: 智能选择最佳实现方式
- **批量处理**: 支持批量签名生成
- **内存管理**: 自动内存分配和清理

## 🔒 安全注意事项

1. **服务器端使用**: 签名逻辑应仅在服务器端使用
2. **密钥保护**: 不要在客户端暴露签名密钥
3. **参数验证**: 始终验证输入参数的有效性
4. **错误处理**: 实现适当的错误处理和日志记录

## 📚 更多文档

- [详细使用指南](docs/WASM_SIGNATURE_USAGE.md)
- [API 参考文档](docs/API_REFERENCE.md)
- [故障排除指南](docs/TROUBLESHOOTING.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。
