/**
 * 聊天相关类型定义
 * 定义聊天消息、选项和响应相关的数据结构
 */

/**
 * 聊天选项配置
 */
export interface ChatOption {
  /** 是否搜索知识库 */
  searchKnowledge: boolean;
  /** 是否搜索所有知识库 */
  searchAllKnowledge: boolean;
  /** 是否搜索共享知识库 */
  searchSharedKnowledge: boolean;
}

/**
 * 聊天请求参数
 */
export interface ChatRequest {
  /** 是否启用流式响应 */
  stream: boolean;
  /** 机器人代码 */
  botCode: string;
  /** 对话ID */
  conversationId: string;
  /** 用户问题 */
  question: string;
  /** 使用的模型 */
  model: string;
  /** 聊天选项 */
  chatOption: ChatOption;
  /** 知识库列表 */
  knowledgeList: unknown[];
  /** 匿名密钥 */
  anonymousKey: string;
  /** UUID */
  uuid: string;
  /** 聊天ID */
  chatId: string;
  /** 文件列表 */
  files: unknown[];
  /** 引用列表 */
  reference: unknown[];
  /** 角色 */
  role: string;
  /** 状态 */
  status: string;
  /** 内容 */
  content: string;
  /** 用户操作 */
  userAction: string;
  /** 代理ID */
  agentId: string;
}

/**
 * SSE消息类型枚举
 */
export enum SSEMessageType {
  /** 消息增量 */
  MESSAGE_DELTA = 'conversation.message.delta',
  /** 聊天完成 */
  CHAT_COMPLETED = 'conversation.chat.completed'
}

/**
 * SSE消息增量数据
 */
export interface SSEMessageDelta {
  /** 角色 */
  role: string;
  /** 类型 */
  type: string;
  /** 内容片段 */
  content: string;
  /** 内容类型 */
  content_type: string;
  /** 消息ID */
  id: string;
  /** 父消息ID */
  parentMsgId: string;
  /** 对话ID */
  conversation_id: string;
  /** 创建时间 */
  created_at: number;
  /** 请求ID */
  requestId: string;
  /** 是否支持下载 */
  supportDownload: boolean;
}

/**
 * SSE聊天完成数据
 */
export interface SSEChatCompleted {
  /** 消息ID */
  id: string;
  /** 父消息ID */
  parentMsgId: string;
  /** 对话ID */
  conversation_id: string;
  /** 是否支持下载 */
  supportDownload: boolean;
}

/**
 * SSE事件数据
 */
export type SSEEventData = SSEMessageDelta | SSEChatCompleted;

/**
 * 聊天响应回调函数类型
 */
export interface ChatCallbacks {
  /** 接收到消息片段时的回调 */
  onMessage?: (content: string, data: SSEMessageDelta) => void;
  /** 聊天完成时的回调 */
  onComplete?: (data: SSEChatCompleted) => void;
  /** 发生错误时的回调 */
  onError?: (error: Error) => void;
}

/**
 * 聊天配置选项
 */
export interface ChatOptions {
  /** 对话ID */
  conversationId: string;
  /** 用户问题 */
  question: string;
  /** 使用的模型，默认为'doubao-1_6-thinking' */
  model?: string;
  /** 机器人代码，默认为'AI_SEARCH' */
  botCode?: string;
  /** 聊天选项配置 */
  chatOption?: Partial<ChatOption>;
  /** 回调函数 */
  callbacks?: ChatCallbacks;
}

/**
 * 聊天响应结果
 */
export interface ChatResponse {
  /** 完整的响应内容 */
  content: string;
  /** 消息ID */
  messageId: string;
  /** 父消息ID */
  parentMessageId: string;
  /** 对话ID */
  conversationId: string;
  /** 请求ID */
  requestId: string;
}
