/**
 * 简单测试示例
 * 验证当贝AI Provider的基本功能
 */

const { DangbeiProvider } = require('../dist');

async function simpleTest() {
  console.log('=== 当贝AI Provider 简单测试 ===\n');

  // 创建Provider实例
  const provider = new DangbeiProvider({
    debug: true
  });

  try {
    // 1. 测试设备配置
    console.log('1. 设备配置测试');
    const deviceConfig = provider.getDeviceConfig();
    console.log('设备ID:', deviceConfig.deviceId);
    console.log('应用版本:', deviceConfig.appVersion);
    console.log('✅ 设备配置测试通过\n');

    // 2. 测试ID生成
    console.log('2. ID生成测试');
    try {
      const id = await provider.generateId();
      console.log('生成的ID:', id);
      console.log('✅ ID生成测试通过\n');
    } catch (error) {
      console.log('⚠️ ID生成失败（可能是网络问题）:', error.message);
      console.log('使用本地ID生成作为备用方案\n');
    }

    // 3. 测试服务状态
    console.log('3. 服务状态测试');
    const status = provider.getStatus();
    console.log('服务状态:', JSON.stringify(status, null, 2));
    console.log('✅ 服务状态测试通过\n');

    // 4. 测试对话创建（可能会因为网络问题失败）
    console.log('4. 对话创建测试');
    try {
      const conversation = await provider.createConversation();
      console.log('对话创建成功:', conversation.conversationId);
      console.log('✅ 对话创建测试通过\n');
      
      // 5. 测试聊天功能
      console.log('5. 聊天功能测试');
      try {
        const response = await provider.chatSync({
          conversationId: conversation.conversationId,
          question: '你好'
        });
        console.log('聊天响应:', response.substring(0, 100) + '...');
        console.log('✅ 聊天功能测试通过\n');
      } catch (chatError) {
        console.log('⚠️ 聊天功能测试失败:', chatError.message);
      }
    } catch (convError) {
      console.log('⚠️ 对话创建失败（可能是网络或API问题）:', convError.message);
      console.log('这是正常的，因为需要真实的API连接\n');
    }

    console.log('✅ 所有基础功能测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理资源
    provider.destroy();
    console.log('🧹 资源已清理');
  }
}

// 运行测试
if (require.main === module) {
  simpleTest()
    .then(() => {
      console.log('\n🎉 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { simpleTest };
