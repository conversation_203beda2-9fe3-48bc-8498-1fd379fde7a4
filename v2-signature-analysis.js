/**
 * v2 接口签名算法排查工具
 * 专门用于分析 chatApi/v2/chat 接口的签名生成逻辑
 */

const crypto = require('crypto');

// 真实的 v2 接口请求数据（来自调用流程.md）
const v2RealRequest = {
  url: '/ai-search/chatApi/v2/chat',
  method: 'POST',
  timestamp: 1755239241,
  nonce: 'QL4MKOwQFtSmnhCOmNjde',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '460D94E7C6980A6973494BC75D075905',
  requestBody: {
    "stream": true,
    "botCode": "AI_SEARCH",
    "conversationId": "363022964585267589",
    "question": "你好!",
    "model": "doubao-1_6-thinking",
    "chatOption": {
      "searchKnowledge": false,
      "searchAllKnowledge": false,
      "searchSharedKnowledge": false
    },
    "knowledgeList": [],
    "anonymousKey": "",
    "uuid": "363022965811450053",
    "chatId": "363022965811450053",
    "files": [],
    "reference": [],
    "role": "user",
    "status": "local",
    "content": "你好!",
    "userAction": "",
    "agentId": ""
  }
};

// v1 接口请求数据（用于对比）
const v1RealRequest = {
  url: '/ai-search/conversationApi/v1/batch/create',
  method: 'POST',
  timestamp: 1755252943,
  nonce: '111g0amuaFUAic500cMI-',
  deviceId: 'eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm',
  sign: '9CC214FB53DDAF31DC1BFE453D14C468',
  requestBody: {
    "conversationList": [{
      "metaData": {
        "chatModelConfig": {},
        "superAgentPath": "/chat"
      },
      "shareId": "",
      "isAnonymous": false,
      "source": ""
    }]
  }
};

console.log('=== v2 接口签名算法排查 ===\n');

/**
 * 测试签名生成函数
 */
function testSignature(description, signString, expectedSign, version = 'v2') {
  console.log(`\n--- ${description} (${version}) ---`);
  console.log(`签名字符串: ${signString}`);
  console.log(`签名字符串长度: ${signString.length}`);
  
  const algorithms = ['md5', 'sha1', 'sha256'];
  
  for (const algo of algorithms) {
    const hash = crypto.createHash(algo).update(signString).digest('hex').toUpperCase();
    console.log(`${algo.toUpperCase()}: ${hash}`);
    
    if (hash === expectedSign) {
      console.log(`🎉 匹配! 算法: ${algo.toUpperCase()}`);
      return true;
    }
  }
  
  console.log('❌ 无匹配');
  return false;
}

/**
 * 标准的 O(e,t) 规范化函数（基于 _app chunk 分析）
 */
function normalizeRequest(method, url, body) {
  if (method === 'GET') {
    const qIndex = url.indexOf('?');
    if (qIndex !== -1) {
      return url.substring(qIndex + 1);
    }
    return '';
  } else if (method === 'POST') {
    return body || '';
  }
  return '';
}

/**
 * 测试 v2 接口的各种签名可能性
 */
function analyzeV2Signature() {
  console.log('1. 测试标准 O(e,t) 规则（POST 使用 body）:');
  
  const bodyString = JSON.stringify(v2RealRequest.requestBody);
  const normalized = normalizeRequest(v2RealRequest.method, v2RealRequest.url, bodyString);
  const signString1 = `${v2RealRequest.timestamp}${normalized}${v2RealRequest.nonce}`;
  
  testSignature('标准规则: timestamp + body + nonce', signString1, v2RealRequest.sign);
  
  console.log('\n2. 测试不同的 body 序列化方式:');
  
  // 测试紧凑格式（无空格）
  const bodyCompact = JSON.stringify(v2RealRequest.requestBody, null, 0);
  const signString2 = `${v2RealRequest.timestamp}${bodyCompact}${v2RealRequest.nonce}`;
  testSignature('紧凑 JSON: timestamp + compactBody + nonce', signString2, v2RealRequest.sign);
  
  // 测试排序后的 JSON
  const bodySorted = JSON.stringify(v2RealRequest.requestBody, Object.keys(v2RealRequest.requestBody).sort());
  const signString3 = `${v2RealRequest.timestamp}${bodySorted}${v2RealRequest.nonce}`;
  testSignature('排序 JSON: timestamp + sortedBody + nonce', signString3, v2RealRequest.sign);
  
  console.log('\n3. 测试包含 URL 路径的规则:');
  
  // 测试包含完整路径
  const signString4 = `${v2RealRequest.timestamp}${v2RealRequest.method} ${v2RealRequest.url}${v2RealRequest.nonce}`;
  testSignature('包含路径: timestamp + "METHOD URL" + nonce', signString4, v2RealRequest.sign);
  
  // 测试只包含路径（不含 method）
  const signString5 = `${v2RealRequest.timestamp}${v2RealRequest.url}${v2RealRequest.nonce}`;
  testSignature('仅路径: timestamp + url + nonce', signString5, v2RealRequest.sign);
  
  console.log('\n4. 测试 v2 特有的规则:');
  
  // 测试是否 v2 不使用 body，而是使用其他规则
  const signString6 = `${v2RealRequest.timestamp}${v2RealRequest.nonce}`;
  testSignature('最简规则: timestamp + nonce', signString6, v2RealRequest.sign);
  
  // 测试包含 deviceId
  const signString7 = `${v2RealRequest.timestamp}${v2RealRequest.deviceId}${v2RealRequest.nonce}`;
  testSignature('包含设备ID: timestamp + deviceId + nonce', signString7, v2RealRequest.sign);
  
  console.log('\n5. 测试版本相关的规则:');
  
  // 测试包含版本号
  const signString8 = `${v2RealRequest.timestamp}v2${normalized}${v2RealRequest.nonce}`;
  testSignature('包含版本: timestamp + "v2" + body + nonce', signString8, v2RealRequest.sign);
  
  // 测试路径中的版本号
  const pathWithoutVersion = v2RealRequest.url.replace('/v2/', '/');
  const signString9 = `${v2RealRequest.timestamp}${pathWithoutVersion}${v2RealRequest.nonce}`;
  testSignature('路径无版本: timestamp + pathWithoutV2 + nonce', signString9, v2RealRequest.sign);
}

/**
 * 对比 v1 和 v2 的差异
 */
function compareV1AndV2() {
  console.log('\n=== v1 vs v2 对比分析 ===');
  
  // 测试 v1 的标准规则
  console.log('\n测试 v1 接口（验证基准）:');
  const v1BodyString = JSON.stringify(v1RealRequest.requestBody);
  const v1Normalized = normalizeRequest(v1RealRequest.method, v1RealRequest.url, v1BodyString);
  const v1SignString = `${v1RealRequest.timestamp}${v1Normalized}${v1RealRequest.nonce}`;
  
  testSignature('v1 标准规则', v1SignString, v1RealRequest.sign, 'v1');
  
  // 分析差异
  console.log('\n差异分析:');
  console.log(`v1 URL: ${v1RealRequest.url}`);
  console.log(`v2 URL: ${v2RealRequest.url}`);
  console.log(`v1 nonce 长度: ${v1RealRequest.nonce.length}`);
  console.log(`v2 nonce 长度: ${v2RealRequest.nonce.length}`);
  console.log(`v1 body 长度: ${JSON.stringify(v1RealRequest.requestBody).length}`);
  console.log(`v2 body 长度: ${JSON.stringify(v2RealRequest.requestBody).length}`);
}

/**
 * 测试 HMAC 算法（可能 v2 使用了密钥）
 */
function testHMACAlgorithms() {
  console.log('\n=== 测试 HMAC 算法（v2 可能使用密钥）===');
  
  const possibleKeys = [
    'v2', 'chat', 'chatApi', 'dangbei-v2', 'ai-v2',
    'dangbei', 'ai', 'api', 'secret', 'key'
  ];
  
  const bodyString = JSON.stringify(v2RealRequest.requestBody);
  const baseString = `${v2RealRequest.timestamp}${bodyString}${v2RealRequest.nonce}`;
  
  for (const key of possibleKeys) {
    const hmacMd5 = crypto.createHmac('md5', key).update(baseString).digest('hex').toUpperCase();
    const hmacSha1 = crypto.createHmac('sha1', key).update(baseString).digest('hex').toUpperCase();
    
    console.log(`密钥 "${key}": MD5=${hmacMd5.substring(0, 8)}... SHA1=${hmacSha1.substring(0, 8)}...`);
    
    if (hmacMd5 === v2RealRequest.sign) {
      console.log(`🎉 找到匹配! HMAC-MD5 密钥: "${key}"`);
      return;
    }
    if (hmacSha1 === v2RealRequest.sign) {
      console.log(`🎉 找到匹配! HMAC-SHA1 密钥: "${key}"`);
      return;
    }
  }
  
  console.log('❌ 未找到匹配的 HMAC 密钥');
}

// 执行分析
analyzeV2Signature();
compareV1AndV2();
testHMACAlgorithms();

console.log('\n=== 分析完成 ===');
console.log('\n建议的下一步调试方法:');
console.log('1. 在浏览器中搜索包含 "v2" 或 "chat" 的签名生成代码');
console.log('2. 查找是否存在版本判断逻辑 if(url.includes("/v2/"))');
console.log('3. 检查是否有专门的 chatApi 签名函数');
console.log('4. 监控网络请求，对比多个 v2 请求的签名规律');
console.log('5. 查找可能的密钥或盐值配置');
