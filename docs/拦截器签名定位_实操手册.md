# 拦截器签名定位·实操手册（当贝AI）

> 目的：提供一份可操作、可复现的文档，指导如何在浏览器中定位 headers.sign 的生成位置（u 值来源），并判断是否存在 secret/salt 或更复杂的规范化规则。

## 目录
- 1. 快速结论与判别
- 2. 前置准备
- 3. 核心路径：从拦截器到 sign(u)
- 4. 进入 I(e,t) 与 O(e,t) 的方法
- 5. 如何判断是否存在 secret
- 6. 高级排查技巧
- 7. 常见陷阱与避坑
- 8. 复核 checklist

---

## 1. 快速结论与判别
- 形态判别：你提供的 sign 为 32 位大写十六进制，典型 **MD5**（SHA1 为 40 位）。
- 线上 _app chunk 证据链：
  - 计算序列为：`timestamp = moment("second").unix()` → `nonce = genNonce()` → `normalized = O(e,t)` → `sign = MD5(`${timestamp}${normalized}${nonce}`).toUpperCase()`。
  - 无直接 secret 常量的痕迹；关键在于 `O(e,t)` 的规范化规则（method/path/query/body 的组合与序列化）。

## 2. 前置准备
- 目标站点：`https://ai.dangbei.com`
- 浏览器：Chrome/Edge（启用开发者工具 DevTools）
- 功能：
  1) Pretty print（美化压缩 JS）
  2) 全局搜索（Ctrl/Cmd + Shift + F）
  3) 断点调试（包括条件断点）

## 3. 核心路径：从拦截器到 sign(u)
1) 打开 DevTools → Sources → 搜索 `_app-*.js`（Next.js 页面级主 chunk）。
2) 打开文件后点击 `{}` 按钮做 **Pretty print**。
3) 全局搜索以下关键字中的任意一个：
   - `interceptors.request.use`
   - `headers.set("sign")` 或 `headers.set('sign')`
   - `headers.set("timestamp")`、`headers.set("nonce")`
4) 在 `headers.set("sign", u)` 这一行的**上一行**打断点。
5) 触发一次 API 请求（例如进入对话页或点击按钮发起请求）。
6) 命中断点后，在 Scope 面板查看局部变量：
   - 一般能看到 `s = await I(e,t)` 返回的 **Map**：`s.get("timestamp") / s.get("nonce") / s.get("sign")`。
   - 此时的 `u` 就等于 `s.get("sign")`。
7) 使用 **Step into**（F11）进入 `I(e,t)` 函数体，继续 **Step into** 进入 `O(e,t)` 与 `S()`（哈希函数）位置。

> 目标：在运行态直接观察 `timestamp / nonce / sign` 的即时值，以及 `O(e,t)` 中“规范化串”的构造逻辑。

## 4. 进入 I(e,t) 与 O(e,t) 的方法
- I(e,t) 常见结构（语义化重构）：
  ```js
  function I(e, t) {
    const ts = moment("second").unix();
    const nonce = genNonce();
    const normalized = O(e, t); // 关键：规范化请求串
    const sign = MD5(`${ts}${normalized}${nonce}`).toUpperCase();
    const map = new Map();
    map.set("timestamp", ts);
    map.set("nonce", nonce);
    map.set("sign", sign);
    map.set("version", "v1");
    return map;
  }
  ```
- O(e,t) 需要重点观察的点：
  1) 是否仅由 `METHOD + ' ' + PATH(+query)` 构成？
  2) 是否引入了 **query 排序/编码** 规则？
  3) 是否引入了 **body 的规范化**（键名排序、去空、布尔/数字格式化）？
  4) 是否拼入了某些 **固定常量/盐**？

> 小技巧：在 `O(e,t)` 内部适当多打几个断点（返回前、关键分支上），对比不同接口/不同 body 的构造结果。

## 5. 如何判断是否存在 secret
- 直接看不到“硬编码 secret 字符串”，但仍可能存在“**隐式 secret 规则**”：
  - 通过对 URL/query/body 的特定规范化（比如固定白名单字段、固定顺序、特殊编码）形成“伪 secret”。
- 判断方法：
  1) 在 `O(e,t)` 中检查是否读到了任何“看似固定”的常量或外部变量（如 token、版本号等）并参与拼接；
  2) 用两三条真实请求样本做对比：只改变一个因素（如一个 query 参数顺序），看 `O(e,t)` 输出是否变化；
  3) 若引入 token/secret，通常能在 `O(e,t)` 或其上游看到获取/读取该值的代码（比如 localStorage、全局变量）。

## 6. 高级排查技巧
- 断点策略：
  - 在 `headers.set("sign"` 行打断点 → 追溯 `u` 的上游调用栈。
  - 在 `Map.prototype.set` 上打断点（Chrome 可在内置原型上断，但需谨慎，干扰较大）。
- Hook/代理思路：
  - 临时在 Console 覆盖 `window.fetch`/`Request` 构造器，打印所有 `method/url/headers`，比对与 `O(e,t)` 的输入是否一致。
  - 覆盖 `CryptoJS.MD5`（若存在）或相关哈希函数，打印入参与返回值（务必刷新后恢复）。
- Source map：
  - 如果站点提供 source map（_next/static/**.map），可尝试切换至源代码视图，定位更快。
- Blackbox：
  - 将第三方库标记为黑盒，减少单步调试时的噪音。
- 条件断点：
  - 只在 `url` 包含 `/ai-search/` 时中断，避免影响其他逻辑。

## 7. 常见陷阱与避坑
- 将完整 URL（含协议与域名）直接参与 `O(e,t)`：
  - 线上通常只取 `pathname(+query)`；若误用完整 URL，会导致我们本地签名与线上差异。
- 时间戳粒度：
  - 线上用秒级 `moment("second").unix()`；若使用毫秒级，需要注意参与签名时的换算。
- token 的影响：
  - 某些场景会设置 `token` 头；若 `O(e,t)` 引入 `token`，需要在无/有 token 两种情况下分别验证。

## 8. 复核 checklist
- [ ] 在 `headers.set("sign"` 前断点，拿到 `timestamp/nonce/sign` 与 `Map` 原值
- [ ] Step into `I(e,t)` → `O(e,t)` → `S()`（哈希）
- [ ] 记录 `O(e,t)` 的构造细则（是否含有 method/path/query/body/常量）
- [ ] 用 2~3 条真实样本对照，验证规则稳定性
- [ ] 更新项目侧 SignatureUtils 的规范化实现，补充中文注释与单测

---

## 附：MD5 vs SHA1 快速识别
- MD5：32 位十六进制（大写/小写皆可）
- SHA1：40 位十六进制
- 你的 sign 示例均为 32 位大写，判定为 MD5 更符合事实。

## 附：项目侧当前对齐策略（便于比对）
- 规范化串：`normalized = METHOD + ' ' + PATH(+query)`
- 签名串：`sign = MD5(`${timestamp}${normalized}${nonce}`).toUpperCase()`
- HttpClient 在请求拦截器传入 `method/url` 用于构造 `normalized`
- 详细见：
  - docs/当贝AI签名_整合分析报告.md
  - 调用流程.md 顶部规则说明

如需，我可以继续补充“图文版动手步骤”，在每一步截图标注断点位置与变量面板，以便非前端同学也能快速复现。
