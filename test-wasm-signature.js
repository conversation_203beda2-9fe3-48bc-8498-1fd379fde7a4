#!/usr/bin/env node

/**
 * 当贝AI WebAssembly 签名模块测试脚本
 * 
 * 这个脚本用于快速测试和验证 WebAssembly 签名模块的功能
 * 包括编译、加载、签名生成等各个环节的测试
 * 
 * <AUTHOR> Provider SDK
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 主测试函数
 */
async function runTests() {
  colorLog('cyan', '🧪 当贝AI WebAssembly 签名模块测试');
  colorLog('cyan', '=' .repeat(50));

  const tests = [
    testEnvironment,
    testWatFile,
    testWasmCompilation,
    testFallbackSignature,
    testUnifiedSignature,
    testPerformance
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (let i = 0; i < tests.length; i++) {
    const test = tests[i];
    colorLog('blue', `\n📋 测试 ${i + 1}/${totalTests}: ${test.name}`);
    
    try {
      await test();
      colorLog('green', '✅ 测试通过');
      passedTests++;
    } catch (error) {
      colorLog('red', `❌ 测试失败: ${error.message}`);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
    }
  }

  // 输出测试结果
  colorLog('cyan', '\n📊 测试结果汇总');
  colorLog('cyan', '-' .repeat(30));
  colorLog('green', `✅ 通过: ${passedTests}/${totalTests}`);
  colorLog('red', `❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    colorLog('green', '\n🎉 所有测试通过！');
    process.exit(0);
  } else {
    colorLog('red', '\n💥 部分测试失败，请检查错误信息');
    process.exit(1);
  }
}

/**
 * 测试运行环境
 */
async function testEnvironment() {
  // 检查 Node.js 版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 14) {
    throw new Error(`Node.js 版本过低: ${nodeVersion}，需要 >= 14.0.0`);
  }
  
  console.log(`   Node.js 版本: ${nodeVersion} ✓`);
  
  // 检查 WebAssembly 支持
  if (typeof WebAssembly === 'undefined') {
    throw new Error('当前环境不支持 WebAssembly');
  }
  
  console.log('   WebAssembly 支持: ✓');
  
  // 检查必要的模块文件
  const requiredFiles = [
    'src/wasm/dangbei-signature-wasm.js',
    'src/wasm/fallback-signature.js',
    'src/wasm/unified-signature.js',
    'scripts/compile-wasm.js'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`缺少必要文件: ${file}`);
    }
  }
  
  console.log('   必要文件检查: ✓');
}

/**
 * 测试 WAT 文件
 */
async function testWatFile() {
  const watPath = 'sign_bg.wat';
  
  if (!fs.existsSync(watPath)) {
    throw new Error(`WAT 文件不存在: ${watPath}`);
  }
  
  const stats = fs.statSync(watPath);
  console.log(`   WAT 文件大小: ${stats.size} 字节`);
  
  // 检查文件内容
  const content = fs.readFileSync(watPath, 'utf8');
  
  if (!content.includes('get_sign')) {
    throw new Error('WAT 文件中未找到 get_sign 函数导出');
  }
  
  console.log('   get_sign 函数导出: ✓');
  
  if (!content.includes('(module')) {
    throw new Error('WAT 文件格式不正确');
  }
  
  console.log('   WAT 文件格式: ✓');
}

/**
 * 测试 WASM 编译
 */
async function testWasmCompilation() {
  const WasmCompiler = require('./scripts/compile-wasm');
  const compiler = new WasmCompiler();
  
  const watPath = 'sign_bg.wat';
  const wasmPath = 'sign_bg_test.wasm';
  
  try {
    // 尝试编译
    await compiler.compile(watPath, wasmPath);
    
    // 验证输出文件
    if (!fs.existsSync(wasmPath)) {
      throw new Error('WASM 文件编译失败');
    }
    
    const stats = fs.statSync(wasmPath);
    console.log(`   WASM 文件大小: ${stats.size} 字节`);
    
    // 验证 WASM 文件
    const isValid = await compiler.validateWasm(wasmPath);
    if (!isValid) {
      throw new Error('WASM 文件验证失败');
    }
    
    console.log('   WASM 文件验证: ✓');
    
    // 获取文件信息
    const info = await compiler.getWasmInfo(wasmPath);
    if (info) {
      console.log(`   导出函数数量: ${info.exportsCount}`);
      console.log(`   导入函数数量: ${info.importsCount}`);
    }
    
  } finally {
    // 清理测试文件
    if (fs.existsSync(wasmPath)) {
      fs.unlinkSync(wasmPath);
    }
  }
}

/**
 * 测试备用签名实现
 */
async function testFallbackSignature() {
  const { FallbackSignature } = require('./src/wasm/fallback-signature');
  
  const signer = new FallbackSignature({ debug: false });
  
  // 测试基础签名
  const signature1 = signer.generateSignature('test_data', 'test_nonce');
  if (!signature1 || typeof signature1 !== 'string') {
    throw new Error('备用签名生成失败');
  }
  
  console.log(`   基础签名: ${signature1.substring(0, 16)}...`);
  
  // 测试签名一致性
  const signature2 = signer.generateSignature('test_data', 'test_nonce');
  if (signature1 !== signature2) {
    throw new Error('备用签名结果不一致');
  }
  
  console.log('   签名一致性: ✓');
  
  // 测试不同输入
  const signature3 = signer.generateSignature('different_data', 'test_nonce');
  if (signature1 === signature3) {
    throw new Error('不同输入产生相同签名');
  }
  
  console.log('   不同输入测试: ✓');
  
  // 测试签名验证
  const isValid = signer.verifySignature('test_data', 'test_nonce', signature1);
  if (!isValid) {
    throw new Error('签名验证失败');
  }
  
  console.log('   签名验证: ✓');
  
  // 测试算法信息
  const info = signer.getAlgorithmInfo();
  if (!info || !info.name) {
    throw new Error('无法获取算法信息');
  }
  
  console.log(`   算法信息: ${info.name} v${info.version}`);
}

/**
 * 测试统一签名接口
 */
async function testUnifiedSignature() {
  const { UnifiedSignature } = require('./src/wasm/unified-signature');
  
  const signer = new UnifiedSignature({
    preferWasm: false, // 强制使用备用实现进行测试
    enableFallback: true,
    debug: false
  });
  
  try {
    // 初始化
    await signer.initialize();
    
    // 检查状态
    const status = signer.getStatus();
    if (!status.isInitialized) {
      throw new Error('统一签名接口初始化失败');
    }
    
    console.log(`   当前模式: ${status.currentMode}`);
    
    // 测试签名生成
    const signature = await signer.generateSignature('test_data', 'test_nonce');
    if (!signature || typeof signature !== 'string') {
      throw new Error('统一接口签名生成失败');
    }
    
    console.log(`   签名生成: ${signature.substring(0, 16)}...`);
    
    // 测试批量签名
    const requests = [
      { param1: 'data1', param2: 'nonce1' },
      { param1: 'data2', param2: 'nonce2' }
    ];
    
    const signatures = await signer.generateSignatureBatch(requests);
    if (!signatures || signatures.length !== 2) {
      throw new Error('批量签名生成失败');
    }
    
    console.log('   批量签名: ✓');
    
    // 测试模式切换
    const switched = signer.switchMode('fallback');
    if (!switched) {
      console.log('   模式切换: 跳过（目标模式不可用）');
    } else {
      console.log('   模式切换: ✓');
    }
    
  } finally {
    signer.destroy();
  }
}

/**
 * 测试性能
 */
async function testPerformance() {
  const { UnifiedSignature } = require('./src/wasm/unified-signature');
  
  const signer = new UnifiedSignature({
    preferWasm: false,
    enableFallback: true,
    debug: false
  });
  
  try {
    await signer.initialize();
    
    // 小规模性能测试
    const iterations = 10;
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await signer.generateSignature('test_data', `nonce_${i}`);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`   ${iterations} 次签名耗时: ${totalTime}ms`);
    console.log(`   平均耗时: ${avgTime.toFixed(2)}ms/次`);
    
    if (avgTime > 100) {
      console.log('   ⚠️ 性能较慢，可能需要优化');
    } else {
      console.log('   性能表现: ✓');
    }
    
    // 内置性能测试
    const perfResults = await signer.performanceTest(5);
    if (perfResults && Object.keys(perfResults).length > 0) {
      console.log('   内置性能测试: ✓');
    }
    
  } finally {
    signer.destroy();
  }
}

/**
 * 错误处理
 */
process.on('uncaughtException', (error) => {
  colorLog('red', `💥 未捕获的异常: ${error.message}`);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `💥 未处理的 Promise 拒绝: ${reason}`);
  if (process.env.DEBUG) {
    console.error('Promise:', promise);
  }
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  runTests().catch((error) => {
    colorLog('red', `💥 测试运行失败: ${error.message}`);
    if (process.env.DEBUG) {
      console.error(error.stack);
    }
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testEnvironment,
  testWatFile,
  testWasmCompilation,
  testFallbackSignature,
  testUnifiedSignature,
  testPerformance
};
